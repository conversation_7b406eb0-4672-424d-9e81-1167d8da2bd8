# AstroGenix Deployment Guide

This guide provides step-by-step instructions for deploying the AstroGenix USDT Staking Platform to a production server.

## 🚀 Quick Start

### Prerequisites
- Ubuntu 20.04+ or CentOS 8+ server
- Root or sudo access
- Domain name pointed to your server
- SSL certificate (Let's Encrypt recommended)

### 1. Server Setup

#### Update System
```bash
sudo apt update && sudo apt upgrade -y
```

#### Install Required Software
```bash
# Install Apache, PHP, MySQL
sudo apt install apache2 php8.1 php8.1-mysql php8.1-curl php8.1-gd php8.1-mbstring php8.1-xml php8.1-zip mysql-server -y

# Enable Apache modules
sudo a2enmod rewrite
sudo a2enmod ssl
sudo a2enmod headers

# Start services
sudo systemctl start apache2
sudo systemctl start mysql
sudo systemctl enable apache2
sudo systemctl enable mysql
```

#### Configure PHP
```bash
sudo nano /etc/php/8.1/apache2/php.ini
```

Update these settings:
```ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
memory_limit = 256M
date.timezone = UTC
```

### 2. Database Setup

#### Secure MySQL Installation
```bash
sudo mysql_secure_installation
```

#### Create Database and User
```bash
sudo mysql -u root -p
```

```sql
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'astrogenix_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON astrogenix.* TO 'astrogenix_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. Application Deployment

#### Clone/Upload Files
```bash
# Create web directory
sudo mkdir -p /var/www/astrogenix

# Upload your files to /var/www/astrogenix
# Or clone from repository
# git clone https://github.com/your-repo/astrogenix.git /var/www/astrogenix
```

#### Set Permissions
```bash
sudo chown -R www-data:www-data /var/www/astrogenix
sudo chmod -R 755 /var/www/astrogenix
sudo chmod -R 777 /var/www/astrogenix/storage
sudo chmod -R 777 /var/www/astrogenix/public/uploads
sudo chmod 644 /var/www/astrogenix/.env
```

#### Configure Environment
```bash
cd /var/www/astrogenix
cp .env.example .env
nano .env
```

Update `.env` with your settings:
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_HOST=localhost
DB_DATABASE=astrogenix
DB_USERNAME=astrogenix_user
DB_PASSWORD=your_secure_password

MAIL_HOST=smtp.gmail.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

USDT_WALLET_ADDRESS=your-usdt-wallet-address
```

#### Import Database
```bash
mysql -u astrogenix_user -p astrogenix < database/schema.sql
mysql -u astrogenix_user -p astrogenix < database/seed_data.sql
```

### 4. Apache Configuration

#### Create Virtual Host
```bash
sudo nano /etc/apache2/sites-available/astrogenix.conf
```

```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    ServerAlias www.yourdomain.com
    DocumentRoot /var/www/astrogenix/public
    
    <Directory /var/www/astrogenix/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/astrogenix_error.log
    CustomLog ${APACHE_LOG_DIR}/astrogenix_access.log combined
</VirtualHost>
```

#### Enable Site
```bash
sudo a2ensite astrogenix.conf
sudo a2dissite 000-default.conf
sudo systemctl reload apache2
```

### 5. SSL Certificate (Let's Encrypt)

#### Install Certbot
```bash
sudo apt install certbot python3-certbot-apache -y
```

#### Obtain Certificate
```bash
sudo certbot --apache -d yourdomain.com -d www.yourdomain.com
```

### 6. Security Hardening

#### Firewall Configuration
```bash
sudo ufw allow OpenSSH
sudo ufw allow 'Apache Full'
sudo ufw enable
```

#### Hide Server Information
```bash
sudo nano /etc/apache2/conf-available/security.conf
```

Update:
```apache
ServerTokens Prod
ServerSignature Off
```

```bash
sudo a2enconf security
sudo systemctl reload apache2
```

#### Secure File Permissions
```bash
sudo find /var/www/astrogenix -type f -exec chmod 644 {} \;
sudo find /var/www/astrogenix -type d -exec chmod 755 {} \;
sudo chmod -R 777 /var/www/astrogenix/storage
sudo chmod -R 777 /var/www/astrogenix/public/uploads
```

### 7. Cron Jobs Setup

#### Edit Crontab
```bash
sudo crontab -e
```

Add these lines:
```bash
# Daily profit distribution (runs at midnight)
0 0 * * * /usr/bin/php /var/www/astrogenix/cron/distribute_profits.php

# Process pending transactions (every hour)
0 * * * * /usr/bin/php /var/www/astrogenix/cron/process_transactions.php

# Send email notifications (every 15 minutes)
*/15 * * * * /usr/bin/php /var/www/astrogenix/cron/send_notifications.php

# Clean up expired sessions (daily at 2 AM)
0 2 * * * /usr/bin/php /var/www/astrogenix/cron/cleanup_sessions.php

# Backup database (daily at 3 AM)
0 3 * * * /usr/bin/mysqldump -u astrogenix_user -pyour_password astrogenix > /var/backups/astrogenix_$(date +\%Y\%m\%d).sql
```

### 8. Monitoring Setup

#### Install Log Rotation
```bash
sudo nano /etc/logrotate.d/astrogenix
```

```
/var/www/astrogenix/storage/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

#### System Monitoring
```bash
# Install htop for system monitoring
sudo apt install htop -y

# Install fail2ban for security
sudo apt install fail2ban -y
sudo systemctl enable fail2ban
```

### 9. Performance Optimization

#### Enable PHP OPcache
```bash
sudo nano /etc/php/8.1/apache2/php.ini
```

Add/update:
```ini
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

#### Configure MySQL
```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

Add under [mysqld]:
```ini
innodb_buffer_pool_size = 256M
query_cache_type = 1
query_cache_size = 64M
```

#### Restart Services
```bash
sudo systemctl restart apache2
sudo systemctl restart mysql
```

### 10. Backup Strategy

#### Create Backup Script
```bash
sudo nano /usr/local/bin/astrogenix-backup.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/astrogenix"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u astrogenix_user -pyour_password astrogenix > $BACKUP_DIR/database_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz -C /var/www astrogenix

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

```bash
sudo chmod +x /usr/local/bin/astrogenix-backup.sh
```

Add to crontab:
```bash
# Full backup daily at 4 AM
0 4 * * * /usr/local/bin/astrogenix-backup.sh
```

## 🔧 Post-Deployment

### 1. Change Default Passwords
- Admin panel: Login with admin/admin123 and change password
- Database: Update database user password
- Update .env file with new credentials

### 2. Configure Email Settings
- Set up SMTP credentials
- Test email functionality
- Configure email templates

### 3. Update USDT Wallet
- Set your USDT wallet address in admin settings
- Test deposit functionality

### 4. Security Checklist
- [ ] SSL certificate installed and working
- [ ] Default passwords changed
- [ ] Firewall configured
- [ ] File permissions set correctly
- [ ] Error reporting disabled in production
- [ ] Database secured
- [ ] Backup system working
- [ ] Monitoring in place

### 5. Testing
- [ ] User registration works
- [ ] Email verification works
- [ ] Investment creation works
- [ ] Admin panel accessible
- [ ] All pages load correctly
- [ ] Mobile responsiveness
- [ ] SSL certificate valid

## 🚨 Troubleshooting

### Common Issues

#### 500 Internal Server Error
```bash
# Check Apache error logs
sudo tail -f /var/log/apache2/error.log

# Check file permissions
sudo chown -R www-data:www-data /var/www/astrogenix
```

#### Database Connection Error
```bash
# Test database connection
mysql -u astrogenix_user -p astrogenix

# Check MySQL status
sudo systemctl status mysql
```

#### Email Not Sending
```bash
# Check PHP mail configuration
php -m | grep mail

# Test SMTP connection
telnet smtp.gmail.com 587
```

#### Cron Jobs Not Running
```bash
# Check cron service
sudo systemctl status cron

# Check cron logs
sudo tail -f /var/log/syslog | grep CRON
```

## 📞 Support

For deployment assistance:
- Email: <EMAIL>
- Documentation: Check `/docs` directory
- Logs: Monitor `/var/www/astrogenix/storage/logs/`

## 🔄 Updates

To update the platform:
1. Backup current installation
2. Upload new files
3. Run database migrations if needed
4. Clear cache
5. Test functionality

Remember to always test updates on a staging environment first!
