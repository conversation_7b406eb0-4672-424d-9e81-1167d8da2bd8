<?php

namespace AstroGenix\Models;

use AstroGenix\Core\Model;
use Exception;

/**
 * User Investment Model
 */
class UserInvestment extends Model
{
    protected $table = 'user_investments';
    protected $fillable = [
        'user_id', 'package_id', 'amount', 'daily_profit',
        'total_profit_expected', 'total_profit_earned',
        'start_date', 'end_date', 'last_profit_date', 'status'
    ];
    
    public function createInvestment(array $data): int
    {
        // Validate required fields
        $required = ['user_id', 'package_id', 'amount'];
        foreach ($required as $field) {
            if (!isset($data[$field])) {
                throw new Exception("Field {$field} is required");
            }
        }
        
        // Get package details
        $packageModel = new InvestmentPackage();
        $package = $packageModel->find($data['package_id']);
        
        if (!$package || $package['status'] !== 'active') {
            throw new Exception('Invalid or inactive investment package');
        }
        
        // Validate investment amount
        if ($data['amount'] < $package['min_amount'] || $data['amount'] > $package['max_amount']) {
            throw new Exception('Investment amount is outside package limits');
        }
        
        // Calculate profits and dates
        $profitData = $packageModel->calculateProfit($data['package_id'], $data['amount']);
        
        $investmentData = [
            'user_id' => $data['user_id'],
            'package_id' => $data['package_id'],
            'amount' => $data['amount'],
            'daily_profit' => $profitData['daily_profit'],
            'total_profit_expected' => $profitData['total_profit'],
            'total_profit_earned' => 0,
            'start_date' => date('Y-m-d H:i:s'),
            'end_date' => $profitData['end_date'],
            'status' => 'active'
        ];
        
        return $this->create($investmentData);
    }
    
    public function getUserInvestments(int $userId, string $status = ''): array
    {
        $conditions = ['user_id' => $userId];
        if ($status) {
            $conditions['status'] = $status;
        }
        
        $sql = "
            SELECT 
                ui.*,
                ip.name as package_name,
                ip.daily_percentage,
                ip.duration_days
            FROM {$this->table} ui
            JOIN investment_packages ip ON ui.package_id = ip.id
            WHERE ui.user_id = :user_id
        ";
        
        $params = ['user_id' => $userId];
        
        if ($status) {
            $sql .= " AND ui.status = :status";
            $params['status'] = $status;
        }
        
        $sql .= " ORDER BY ui.created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getActiveInvestments(int $userId = null): array
    {
        $sql = "
            SELECT 
                ui.*,
                ip.name as package_name,
                ip.daily_percentage,
                u.username
            FROM {$this->table} ui
            JOIN investment_packages ip ON ui.package_id = ip.id
            JOIN users u ON ui.user_id = u.id
            WHERE ui.status = 'active'
        ";
        
        $params = [];
        
        if ($userId) {
            $sql .= " AND ui.user_id = :user_id";
            $params['user_id'] = $userId;
        }
        
        $sql .= " ORDER BY ui.created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function getInvestmentDetails(int $investmentId): ?array
    {
        $sql = "
            SELECT 
                ui.*,
                ip.name as package_name,
                ip.description as package_description,
                ip.daily_percentage,
                ip.duration_days,
                ip.total_return_percentage,
                u.username,
                u.email
            FROM {$this->table} ui
            JOIN investment_packages ip ON ui.package_id = ip.id
            JOIN users u ON ui.user_id = u.id
            WHERE ui.id = :id
        ";
        
        return $this->db->fetch($sql, ['id' => $investmentId]);
    }
    
    public function getInvestmentProgress(int $investmentId): array
    {
        $investment = $this->find($investmentId);
        if (!$investment) {
            throw new Exception('Investment not found');
        }
        
        $startDate = new \DateTime($investment['start_date']);
        $endDate = new \DateTime($investment['end_date']);
        $currentDate = new \DateTime();
        
        $totalDays = $startDate->diff($endDate)->days;
        $elapsedDays = $startDate->diff($currentDate)->days;
        
        if ($elapsedDays > $totalDays) {
            $elapsedDays = $totalDays;
        }
        
        $progressPercentage = $totalDays > 0 ? ($elapsedDays / $totalDays) * 100 : 0;
        $remainingDays = max(0, $totalDays - $elapsedDays);
        
        return [
            'total_days' => $totalDays,
            'elapsed_days' => $elapsedDays,
            'remaining_days' => $remainingDays,
            'progress_percentage' => round($progressPercentage, 2),
            'is_completed' => $progressPercentage >= 100 || $investment['status'] === 'completed'
        ];
    }
    
    public function getInvestmentsDueForProfit(): array
    {
        $sql = "
            SELECT * FROM {$this->table}
            WHERE status = 'active'
            AND (last_profit_date IS NULL OR last_profit_date < CURDATE())
            AND start_date <= NOW()
            AND end_date > NOW()
        ";
        
        return $this->db->fetchAll($sql);
    }
    
    public function markProfitDistributed(int $investmentId, float $profitAmount): bool
    {
        $investment = $this->find($investmentId);
        if (!$investment) {
            return false;
        }
        
        $newTotalEarned = $investment['total_profit_earned'] + $profitAmount;
        
        $updateData = [
            'total_profit_earned' => $newTotalEarned,
            'last_profit_date' => date('Y-m-d')
        ];
        
        // Check if investment is completed
        if ($newTotalEarned >= $investment['total_profit_expected'] || 
            strtotime($investment['end_date']) <= time()) {
            $updateData['status'] = 'completed';
        }
        
        return $this->update($investmentId, $updateData);
    }
    
    public function getUserInvestmentStats(int $userId): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_investments,
                SUM(amount) as total_invested,
                SUM(total_profit_earned) as total_earned,
                SUM(CASE WHEN status = 'active' THEN amount ELSE 0 END) as active_investment,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                AVG(amount) as avg_investment
            FROM {$this->table}
            WHERE user_id = :user_id
        ";
        
        return $this->db->fetch($sql, ['user_id' => $userId]) ?: [];
    }
    
    public function getSystemInvestmentStats(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_investments,
                SUM(amount) as total_invested,
                SUM(total_profit_earned) as total_profits_paid,
                SUM(total_profit_expected) as total_profits_expected,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_investments,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_investments,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_investments,
                AVG(amount) as avg_investment
            FROM {$this->table}
        ";
        
        return $this->db->fetch($sql) ?: [];
    }
    
    public function getRecentInvestments(int $limit = 10): array
    {
        $sql = "
            SELECT 
                ui.amount,
                ui.created_at,
                ip.name as package_name,
                u.username
            FROM {$this->table} ui
            JOIN investment_packages ip ON ui.package_id = ip.id
            JOIN users u ON ui.user_id = u.id
            ORDER BY ui.created_at DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, ['limit' => $limit]);
    }
    
    public function cancelInvestment(int $investmentId, int $adminId = null): bool
    {
        $investment = $this->find($investmentId);
        if (!$investment || $investment['status'] !== 'active') {
            return false;
        }
        
        $this->db->beginTransaction();
        
        try {
            // Update investment status
            $this->update($investmentId, ['status' => 'cancelled']);
            
            // Refund the principal amount to user
            $userModel = new User();
            $userModel->updateBalance($investment['user_id'], $investment['amount'], 'add');
            
            // Log the cancellation
            $transactionModel = new Transaction();
            $transactionModel->create([
                'user_id' => $investment['user_id'],
                'type' => 'refund',
                'amount' => $investment['amount'],
                'status' => 'completed',
                'admin_notes' => 'Investment cancelled - principal refunded',
                'processed_by' => $adminId,
                'processed_at' => date('Y-m-d H:i:s')
            ]);
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    public function getTopInvestments(int $limit = 10): array
    {
        $sql = "
            SELECT 
                ui.amount,
                ui.total_profit_earned,
                ui.created_at,
                ip.name as package_name,
                u.username
            FROM {$this->table} ui
            JOIN investment_packages ip ON ui.package_id = ip.id
            JOIN users u ON ui.user_id = u.id
            ORDER BY ui.amount DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, ['limit' => $limit]);
    }
}
