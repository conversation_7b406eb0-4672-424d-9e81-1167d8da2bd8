[PHP]
; PHP Configuration for AstroGenix

; Basic Settings
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
post_max_size = 10M
upload_max_filesize = 10M
max_file_uploads = 20

; Error Reporting
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/apache2/php_errors.log

; Session Configuration
session.save_handler = files
session.save_path = "/var/www/html/storage/sessions"
session.use_cookies = 1
session.use_only_cookies = 1
session.cookie_lifetime = 0
session.cookie_secure = 1
session.cookie_httponly = 1
session.cookie_samesite = "Lax"
session.gc_maxlifetime = 7200

; Security
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Date & Time
date.timezone = UTC

; OPcache Configuration
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
opcache.validate_timestamps = 0

; Realpath Cache
realpath_cache_size = 4096K
realpath_cache_ttl = 600
