<?php

namespace AstroGenix\Core;

/**
 * HTTP Request Handler
 */
class Request
{
    private $method;
    private $uri;
    private $params;
    private $query;
    private $body;
    private $headers;
    private $files;
    
    public function __construct()
    {
        $this->method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        $this->uri = parse_url($_SERVER['REQUEST_URI'] ?? '/', PHP_URL_PATH);
        $this->query = $_GET;
        $this->body = $this->parseBody();
        $this->headers = $this->parseHeaders();
        $this->files = $_FILES;
        $this->params = [];
    }
    
    public function getMethod(): string
    {
        return $this->method;
    }
    
    public function getUri(): string
    {
        return $this->uri;
    }
    
    public function isGet(): bool
    {
        return $this->method === 'GET';
    }
    
    public function isPost(): bool
    {
        return $this->method === 'POST';
    }
    
    public function isPut(): bool
    {
        return $this->method === 'PUT';
    }
    
    public function isDelete(): bool
    {
        return $this->method === 'DELETE';
    }
    
    public function isAjax(): bool
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    public function isJson(): bool
    {
        return strpos($this->getHeader('Content-Type'), 'application/json') !== false;
    }
    
    public function get(string $key, $default = null)
    {
        return $this->query[$key] ?? $default;
    }
    
    public function post(string $key, $default = null)
    {
        return $this->body[$key] ?? $default;
    }
    
    public function input(string $key, $default = null)
    {
        return $this->post($key) ?? $this->get($key) ?? $default;
    }
    
    public function all(): array
    {
        return array_merge($this->query, $this->body);
    }
    
    public function only(array $keys): array
    {
        $data = $this->all();
        return array_intersect_key($data, array_flip($keys));
    }
    
    public function except(array $keys): array
    {
        $data = $this->all();
        return array_diff_key($data, array_flip($keys));
    }
    
    public function has(string $key): bool
    {
        return isset($this->query[$key]) || isset($this->body[$key]);
    }
    
    public function filled(string $key): bool
    {
        return $this->has($key) && !empty($this->input($key));
    }
    
    public function getHeader(string $name): ?string
    {
        $name = strtolower($name);
        return $this->headers[$name] ?? null;
    }
    
    public function getHeaders(): array
    {
        return $this->headers;
    }
    
    public function file(string $key): ?array
    {
        return $this->files[$key] ?? null;
    }
    
    public function hasFile(string $key): bool
    {
        return isset($this->files[$key]) && $this->files[$key]['error'] === UPLOAD_ERR_OK;
    }
    
    public function ip(): string
    {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    public function userAgent(): string
    {
        return $_SERVER['HTTP_USER_AGENT'] ?? '';
    }
    
    public function referer(): string
    {
        return $_SERVER['HTTP_REFERER'] ?? '';
    }
    
    public function getParam(string $key, $default = null)
    {
        return $this->params[$key] ?? $default;
    }
    
    public function setParam(string $key, $value): void
    {
        $this->params[$key] = $value;
    }
    
    public function setParams(array $params): void
    {
        $this->params = array_merge($this->params, $params);
    }
    
    public function getParams(): array
    {
        return $this->params;
    }
    
    public function validate(array $rules): array
    {
        $validator = new Validator();
        return $validator->validate($this->all(), $rules);
    }
    
    public function sanitize(string $key): string
    {
        $value = $this->input($key, '');
        return htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
    }
    
    public function sanitizeAll(): array
    {
        $data = $this->all();
        array_walk_recursive($data, function(&$value) {
            if (is_string($value)) {
                $value = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
            }
        });
        return $data;
    }
    
    private function parseBody(): array
    {
        if ($this->isJson()) {
            $json = file_get_contents('php://input');
            return json_decode($json, true) ?? [];
        }
        
        return $_POST;
    }
    
    private function parseHeaders(): array
    {
        $headers = [];
        
        foreach ($_SERVER as $key => $value) {
            if (strpos($key, 'HTTP_') === 0) {
                $header = str_replace('_', '-', substr($key, 5));
                $header = strtolower($header);
                $headers[$header] = $value;
            }
        }
        
        return $headers;
    }
    
    public function getScheme(): string
    {
        return isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' ? 'https' : 'http';
    }
    
    public function getHost(): string
    {
        return $_SERVER['HTTP_HOST'] ?? 'localhost';
    }
    
    public function getPort(): int
    {
        return (int) ($_SERVER['SERVER_PORT'] ?? 80);
    }
    
    public function getFullUrl(): string
    {
        return $this->getScheme() . '://' . $this->getHost() . $_SERVER['REQUEST_URI'];
    }
    
    public function getBaseUrl(): string
    {
        return $this->getScheme() . '://' . $this->getHost();
    }
}
