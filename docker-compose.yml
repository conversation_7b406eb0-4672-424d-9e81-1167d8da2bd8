version: '3.8'

services:
  # Web Server (Apache + PHP)
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: astrogenix_web
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/apache/sites-available:/etc/apache2/sites-available
      - ./docker/ssl:/etc/ssl/certs
      - ./storage/logs:/var/log/apache2
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html/public
    depends_on:
      - database
      - redis
    networks:
      - astrogenix_network

  # MySQL Database
  database:
    image: mysql:8.0
    container_name: astrogenix_db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-astrogenix_root_2024}
      MYSQL_DATABASE: ${DB_DATABASE:-astrogenix}
      MYSQL_USER: ${DB_USERNAME:-astrogenix_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-astrogenix_pass_2024}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/seed_data.sql:/docker-entrypoint-initdb.d/02-seed.sql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf
    ports:
      - "3306:3306"
    networks:
      - astrogenix_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: astrogenix_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-astrogenix_redis_2024}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - astrogenix_network

  # phpMyAdmin (Optional - for database management)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: astrogenix_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: database
      PMA_PORT: 3306
      PMA_USER: ${DB_USERNAME:-astrogenix_user}
      PMA_PASSWORD: ${DB_PASSWORD:-astrogenix_pass_2024}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-astrogenix_root_2024}
    ports:
      - "8080:80"
    depends_on:
      - database
    networks:
      - astrogenix_network

  # Cron Jobs Container
  cron:
    build:
      context: .
      dockerfile: Dockerfile.cron
    container_name: astrogenix_cron
    restart: unless-stopped
    volumes:
      - ./:/var/www/html
      - ./docker/cron/crontab:/etc/cron.d/astrogenix
    environment:
      - DB_HOST=database
      - DB_DATABASE=${DB_DATABASE:-astrogenix}
      - DB_USERNAME=${DB_USERNAME:-astrogenix_user}
      - DB_PASSWORD=${DB_PASSWORD:-astrogenix_pass_2024}
    depends_on:
      - database
      - web
    networks:
      - astrogenix_network

  # Nginx Reverse Proxy (Optional - for production)
  nginx:
    image: nginx:alpine
    container_name: astrogenix_nginx
    restart: unless-stopped
    ports:
      - "8443:443"
      - "8000:80"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/sites:/etc/nginx/conf.d
      - ./docker/ssl:/etc/ssl/certs
    depends_on:
      - web
    networks:
      - astrogenix_network
    profiles:
      - production

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  astrogenix_network:
    driver: bridge
