<?php

namespace AstroGenix\Models;

use AstroGenix\Core\Model;
use Exception;

/**
 * Transaction Model
 */
class Transaction extends Model
{
    protected $table = 'transactions';
    protected $fillable = [
        'user_id', 'type', 'amount', 'status', 'transaction_hash',
        'screenshot', 'admin_notes', 'processed_by', 'processed_at'
    ];
    
    public function createDeposit(int $userId, float $amount, string $transactionHash = '', string $screenshot = ''): int
    {
        if ($amount <= 0) {
            throw new Exception('Deposit amount must be greater than zero');
        }
        
        $data = [
            'user_id' => $userId,
            'type' => 'deposit',
            'amount' => $amount,
            'status' => 'pending',
            'transaction_hash' => $transactionHash,
            'screenshot' => $screenshot
        ];
        
        return $this->create($data);
    }
    
    public function createWithdrawal(int $userId, float $amount): int
    {
        if ($amount <= 0) {
            throw new Exception('Withdrawal amount must be greater than zero');
        }
        
        // Check user balance
        $userModel = new User();
        $user = $userModel->find($userId);
        
        if (!$user || $user['balance_usdt'] < $amount) {
            throw new Exception('Insufficient balance');
        }
        
        $this->db->beginTransaction();
        
        try {
            // Deduct amount from user balance
            $userModel->updateBalance($userId, $amount, 'subtract');
            
            // Create withdrawal transaction
            $transactionId = $this->create([
                'user_id' => $userId,
                'type' => 'withdrawal',
                'amount' => $amount,
                'status' => 'pending'
            ]);
            
            $this->db->commit();
            return $transactionId;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    public function createProfitTransaction(int $userId, float $amount, int $investmentId = null): int
    {
        $data = [
            'user_id' => $userId,
            'type' => 'profit',
            'amount' => $amount,
            'status' => 'completed',
            'admin_notes' => $investmentId ? "Daily profit from investment #{$investmentId}" : 'Daily profit'
        ];
        
        return $this->create($data);
    }
    
    public function createReferralCommission(int $userId, float $amount, int $referredUserId): int
    {
        $data = [
            'user_id' => $userId,
            'type' => 'referral',
            'amount' => $amount,
            'status' => 'completed',
            'admin_notes' => "Referral commission from user #{$referredUserId}"
        ];
        
        return $this->create($data);
    }
    
    public function createBonus(int $userId, float $amount, string $reason = ''): int
    {
        $data = [
            'user_id' => $userId,
            'type' => 'bonus',
            'amount' => $amount,
            'status' => 'completed',
            'admin_notes' => $reason ?: 'Admin bonus'
        ];
        
        return $this->create($data);
    }
    
    public function getUserTransactions(int $userId, string $type = '', int $page = 1, int $perPage = 20): array
    {
        $sql = "
            SELECT 
                t.*,
                u.username,
                a.username as processed_by_username
            FROM {$this->table} t
            JOIN users u ON t.user_id = u.id
            LEFT JOIN admin_users a ON t.processed_by = a.id
            WHERE t.user_id = :user_id
        ";
        
        $params = ['user_id' => $userId];
        
        if ($type) {
            $sql .= " AND t.type = :type";
            $params['type'] = $type;
        }
        
        $sql .= " ORDER BY t.created_at DESC";
        
        return $this->db->paginate($sql, $params, $page, $perPage);
    }
    
    public function getPendingTransactions(string $type = ''): array
    {
        $sql = "
            SELECT 
                t.*,
                u.username,
                u.email
            FROM {$this->table} t
            JOIN users u ON t.user_id = u.id
            WHERE t.status = 'pending'
        ";
        
        $params = [];
        
        if ($type) {
            $sql .= " AND t.type = :type";
            $params['type'] = $type;
        }
        
        $sql .= " ORDER BY t.created_at ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    public function approveTransaction(int $transactionId, int $adminId, string $notes = ''): bool
    {
        $transaction = $this->find($transactionId);
        if (!$transaction || $transaction['status'] !== 'pending') {
            return false;
        }
        
        $this->db->beginTransaction();
        
        try {
            // Update transaction status
            $this->update($transactionId, [
                'status' => 'approved',
                'processed_by' => $adminId,
                'processed_at' => date('Y-m-d H:i:s'),
                'admin_notes' => $notes
            ]);
            
            // If it's a deposit, add to user balance
            if ($transaction['type'] === 'deposit') {
                $userModel = new User();
                $userModel->updateBalance($transaction['user_id'], $transaction['amount'], 'add');
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    public function rejectTransaction(int $transactionId, int $adminId, string $reason = ''): bool
    {
        $transaction = $this->find($transactionId);
        if (!$transaction || $transaction['status'] !== 'pending') {
            return false;
        }
        
        $this->db->beginTransaction();
        
        try {
            // Update transaction status
            $this->update($transactionId, [
                'status' => 'rejected',
                'processed_by' => $adminId,
                'processed_at' => date('Y-m-d H:i:s'),
                'admin_notes' => $reason ?: 'Transaction rejected'
            ]);
            
            // If it's a withdrawal, refund to user balance
            if ($transaction['type'] === 'withdrawal') {
                $userModel = new User();
                $userModel->updateBalance($transaction['user_id'], $transaction['amount'], 'add');
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    public function getTransactionStatistics(): array
    {
        $sql = "
            SELECT 
                type,
                status,
                COUNT(*) as count,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount
            FROM {$this->table}
            GROUP BY type, status
            ORDER BY type, status
        ";
        
        $results = $this->db->fetchAll($sql);
        
        // Organize results by type and status
        $statistics = [];
        foreach ($results as $result) {
            $statistics[$result['type']][$result['status']] = [
                'count' => $result['count'],
                'total_amount' => $result['total_amount'],
                'avg_amount' => $result['avg_amount']
            ];
        }
        
        return $statistics;
    }
    
    public function getDailyTransactionStats(int $days = 30): array
    {
        $sql = "
            SELECT 
                DATE(created_at) as date,
                type,
                COUNT(*) as count,
                SUM(amount) as total_amount
            FROM {$this->table}
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY DATE(created_at), type
            ORDER BY date DESC, type
        ";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    public function getRecentTransactions(int $limit = 10): array
    {
        $sql = "
            SELECT 
                t.*,
                u.username
            FROM {$this->table} t
            JOIN users u ON t.user_id = u.id
            ORDER BY t.created_at DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, ['limit' => $limit]);
    }
    
    public function getUserTransactionSummary(int $userId): array
    {
        $sql = "
            SELECT 
                type,
                status,
                COUNT(*) as count,
                SUM(amount) as total_amount
            FROM {$this->table}
            WHERE user_id = :user_id
            GROUP BY type, status
        ";
        
        $results = $this->db->fetchAll($sql, ['user_id' => $userId]);
        
        $summary = [
            'total_deposits' => 0,
            'total_withdrawals' => 0,
            'total_profits' => 0,
            'total_referrals' => 0,
            'total_bonuses' => 0,
            'pending_deposits' => 0,
            'pending_withdrawals' => 0
        ];
        
        foreach ($results as $result) {
            $key = $result['type'] === 'deposit' && $result['status'] === 'approved' ? 'total_deposits' :
                   ($result['type'] === 'withdrawal' && $result['status'] === 'approved' ? 'total_withdrawals' :
                   ($result['type'] === 'profit' ? 'total_profits' :
                   ($result['type'] === 'referral' ? 'total_referrals' :
                   ($result['type'] === 'bonus' ? 'total_bonuses' :
                   ($result['type'] === 'deposit' && $result['status'] === 'pending' ? 'pending_deposits' :
                   ($result['type'] === 'withdrawal' && $result['status'] === 'pending' ? 'pending_withdrawals' : null))))));
            
            if ($key) {
                $summary[$key] = $result['total_amount'];
            }
        }
        
        return $summary;
    }
    
    public function getMonthlyTransactionReport(int $year, int $month): array
    {
        $sql = "
            SELECT 
                DAY(created_at) as day,
                type,
                status,
                COUNT(*) as count,
                SUM(amount) as total_amount
            FROM {$this->table}
            WHERE YEAR(created_at) = :year AND MONTH(created_at) = :month
            GROUP BY DAY(created_at), type, status
            ORDER BY day, type, status
        ";
        
        return $this->db->fetchAll($sql, ['year' => $year, 'month' => $month]);
    }
}
