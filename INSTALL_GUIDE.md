# 🚀 AstroGenix Installation Guide

Добро пожаловать в руководство по установке платформы AstroGenix! У вас есть несколько способов установки на выбор.

## 📋 Варианты установки

### 1. 🌐 Веб-установщик (Рекомендуется для новичков)
### 2. 🖥️ Автоматический серверный скрипт (Для опытных пользователей)
### 3. 🐳 Docker установка (Для разработчиков)
### 4. 📝 Ручная установка (Для экспертов)

---

## 🌐 Метод 1: Веб-установщик

Самый простой способ установки через браузер.

### Шаги:
1. **Загрузите файлы** на ваш веб-сервер
2. **Откройте браузер** и перейдите на `http://yourdomain.com/install.php`
3. **Следуйте инструкциям** мастера установки:
   - Проверка системных требований
   - Настройка базы данных
   - Конфигурация приложения
   - Создание админ-аккаунта
   - Финальная настройка

### Требования:
- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx с mod_rewrite
- Права на запись в папки

### Преимущества:
✅ Простота использования  
✅ Пошаговые инструкции  
✅ Автоматическая проверка требований  
✅ Не требует технических знаний  

---

## 🖥️ Метод 2: Автоматический серверный скрипт

Полностью автоматизированная установка на Ubuntu/Debian сервер.

### Быстрый старт:
```bash
# Скачайте файлы проекта
wget https://github.com/your-repo/astrogenix/archive/main.zip
unzip main.zip
cd astrogenix-main

# Запустите установщик
sudo bash install.sh
```

### Что делает скрипт:
- ✅ Обновляет систему
- ✅ Устанавливает Apache, PHP 8.1, MySQL
- ✅ Настраивает базу данных
- ✅ Конфигурирует веб-сервер
- ✅ Устанавливает SSL сертификат
- ✅ Настраивает firewall
- ✅ Создает cron задачи
- ✅ Оптимизирует производительность

### Интерактивная настройка:
Скрипт запросит у вас:
- Доменное имя
- Email администратора
- Пароли для MySQL и админ-панели
- USDT кошелек

### Время установки: ~10-15 минут

---

## 🐳 Метод 3: Docker установка

Идеально для разработки и тестирования.

### Быстрый запуск:
```bash
# Клонируйте репозиторий
git clone https://github.com/your-repo/astrogenix.git
cd astrogenix

# Создайте .env файл
cp .env.example .env
nano .env  # Отредактируйте настройки

# Запустите контейнеры
docker-compose up -d
```

### Доступные сервисы:
- **Веб-сайт**: http://localhost
- **phpMyAdmin**: http://localhost:8080
- **MySQL**: localhost:3306
- **Redis**: localhost:6379

### Управление:
```bash
# Просмотр логов
docker-compose logs -f

# Остановка
docker-compose down

# Перезапуск
docker-compose restart

# Обновление
docker-compose pull && docker-compose up -d
```

### Преимущества Docker:
✅ Изолированная среда  
✅ Легкое развертывание  
✅ Консистентность между окружениями  
✅ Простое масштабирование  

---

## 📝 Метод 4: Ручная установка

Для опытных администраторов, которые хотят полный контроль.

### Подробные инструкции:
См. файл `DEPLOYMENT.md` для пошагового руководства по ручной установке.

### Основные шаги:
1. Установка системных требований
2. Настройка веб-сервера
3. Конфигурация базы данных
4. Загрузка файлов приложения
5. Настройка окружения
6. Импорт схемы БД
7. Настройка безопасности
8. Конфигурация cron задач

---

## 🔧 После установки

### Обязательные шаги:
1. **Смените пароль администратора**
2. **Удалите файл install.php** (если использовали веб-установщик)
3. **Настройте SMTP** для отправки email
4. **Обновите USDT кошелек** в настройках
5. **Протестируйте функциональность**

### Доступ к админ-панели:
- URL: `https://yourdomain.com/admin`
- Email: <EMAIL> (по умолчанию)
- Пароль: admin123 (по умолчанию - ОБЯЗАТЕЛЬНО СМЕНИТЕ!)

### Настройка инвестиционных пакетов:
1. Войдите в админ-панель
2. Перейдите в "Investment Packages"
3. Настройте параметры пакетов
4. Активируйте нужные пакеты

---

## 🛡️ Безопасность

### Обязательные меры:
- [ ] Смена всех паролей по умолчанию
- [ ] Настройка SSL сертификата
- [ ] Конфигурация firewall
- [ ] Регулярные бэкапы
- [ ] Мониторинг логов
- [ ] Обновление системы

### Рекомендуемые настройки:
- Двухфакторная аутентификация
- Ограничение доступа к админ-панели по IP
- Регулярная смена паролей
- Мониторинг подозрительной активности

---

## 🔍 Устранение неполадок

### Частые проблемы:

#### 500 Internal Server Error
```bash
# Проверьте права доступа
sudo chown -R www-data:www-data /var/www/astrogenix
sudo chmod -R 755 /var/www/astrogenix
sudo chmod -R 777 /var/www/astrogenix/storage

# Проверьте логи Apache
sudo tail -f /var/log/apache2/error.log
```

#### Ошибка подключения к БД
```bash
# Проверьте статус MySQL
sudo systemctl status mysql

# Проверьте настройки в .env
nano .env

# Тест подключения
mysql -u username -p database_name
```

#### Email не отправляются
- Проверьте SMTP настройки в .env
- Убедитесь, что порты не заблокированы
- Проверьте логи email

#### Cron задачи не работают
```bash
# Проверьте статус cron
sudo systemctl status cron

# Просмотрите логи cron
sudo tail -f /var/log/syslog | grep CRON

# Проверьте crontab
sudo crontab -l -u www-data
```

---

## 📞 Поддержка

### Получить помощь:
- **Email**: <EMAIL>
- **Документация**: Проверьте папку `/docs`
- **Логи**: Мониторьте `/storage/logs/`
- **GitHub Issues**: Сообщайте о багах

### Полезные команды:
```bash
# Просмотр логов приложения
tail -f storage/logs/astrogenix.log

# Проверка статуса сервисов
sudo systemctl status apache2 mysql

# Тест email
php -r "mail('<EMAIL>', 'Test', 'Test message');"

# Очистка кэша
rm -rf storage/cache/*
```

---

## 🎯 Рекомендации по производительности

### Для высоких нагрузок:
1. **Используйте Redis** для кэширования
2. **Настройте CDN** для статических файлов
3. **Оптимизируйте MySQL** конфигурацию
4. **Включите OPcache** для PHP
5. **Используйте HTTP/2** и Gzip сжатие

### Мониторинг:
- Установите систему мониторинга (Zabbix, Nagios)
- Настройте алерты для критических событий
- Регулярно проверяйте производительность

---

## 🔄 Обновления

### Процесс обновления:
1. Создайте бэкап
2. Загрузите новые файлы
3. Выполните миграции БД (если есть)
4. Очистите кэш
5. Протестируйте функциональность

### Автоматические обновления:
Настройте уведомления о новых версиях в админ-панели.

---

## ✅ Чек-лист после установки

- [ ] Платформа доступна по HTTPS
- [ ] Админ-панель работает
- [ ] Регистрация пользователей работает
- [ ] Email уведомления отправляются
- [ ] Инвестиционные пакеты настроены
- [ ] Cron задачи запущены
- [ ] Бэкапы настроены
- [ ] Безопасность настроена
- [ ] Мониторинг включен
- [ ] Документация изучена

---

**🎉 Поздравляем! Ваша платформа AstroGenix готова к работе!**

Не забудьте поделиться успехом с нашим сообществом и оставить отзыв о процессе установки.
