<?php
/**
 * Quick Database Reset Script for AstroGenix
 * This script quickly resets the database without web interface
 * Usage: php quick_reset.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "AstroGenix Quick Database Reset\n";
echo "==============================\n\n";

// Load configuration
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $lines = explode("\n", $envContent);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"');
        }
    }
} else {
    echo "❌ .env file not found. Please run installation first.\n";
    exit(1);
}

// Database configuration
$dbConfig = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'port' => $_ENV['DB_PORT'] ?? '3306',
    'database' => $_ENV['DB_DATABASE'] ?? 'astrogenix',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? ''
];

try {
    echo "Connecting to database...\n";
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Connected successfully\n\n";

    // Disable foreign key checks
    echo "Disabling foreign key checks...\n";
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");

    // Drop all tables
    echo "Dropping existing tables...\n";
    $tables = [
        'support_messages', 'support_tickets', 'news', 'news_categories',
        'user_tasks', 'tasks', 'referral_commissions', 'transactions',
        'daily_profits', 'user_investments', 'investment_packages',
        'users', 'system_settings', 'admin_users'
    ];

    foreach ($tables as $table) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS `$table`");
            echo "✓ Dropped table: $table\n";
        } catch (PDOException $e) {
            echo "⚠ Warning dropping $table: " . $e->getMessage() . "\n";
        }
    }

    // Re-enable foreign key checks
    echo "\nRe-enabling foreign key checks...\n";
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");

    // Recreate tables from schema
    echo "\nRecreating tables from schema...\n";
    
    $schema = file_get_contents('database/schema.sql');
    
    // Remove comments and empty lines
    $lines = explode("\n", $schema);
    $cleanedSql = '';
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }
        $cleanedSql .= $line . "\n";
    }
    
    // Split SQL into individual statements
    $statements = explode(';', $cleanedSql);
    
    // Separate table creation from foreign key constraints
    $createStatements = [];
    $alterStatements = [];
    $otherStatements = [];
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement)) continue;
        
        if (stripos($statement, 'CREATE TABLE') !== false) {
            $createStatements[] = $statement;
        } elseif (stripos($statement, 'ALTER TABLE') !== false && stripos($statement, 'FOREIGN KEY') !== false) {
            $alterStatements[] = $statement;
        } elseif (stripos($statement, 'CREATE DATABASE') === false && stripos($statement, 'USE ') === false) {
            $otherStatements[] = $statement;
        }
    }
    
    // Execute in order: create tables, other statements, then foreign keys
    foreach ($createStatements as $statement) {
        try {
            $pdo->exec($statement);
            preg_match('/CREATE TABLE `?(\w+)`?/', $statement, $matches);
            if (isset($matches[1])) {
                echo "✓ Created table: {$matches[1]}\n";
            }
        } catch (PDOException $e) {
            echo "✗ Error creating table: " . $e->getMessage() . "\n";
        }
    }
    
    foreach ($otherStatements as $statement) {
        try {
            $pdo->exec($statement);
        } catch (PDOException $e) {
            echo "⚠ Warning: " . $e->getMessage() . "\n";
        }
    }
    
    foreach ($alterStatements as $statement) {
        try {
            $pdo->exec($statement);
        } catch (PDOException $e) {
            echo "⚠ Warning adding constraint: " . $e->getMessage() . "\n";
        }
    }

    // Insert initial data
    echo "\nInserting initial data...\n";
    
    $seedData = file_get_contents('database/seed_data.sql');
    
    // Remove comments and empty lines
    $lines = explode("\n", $seedData);
    $cleanedSql = '';
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }
        $cleanedSql .= $line . "\n";
    }
    
    $statements = explode(';', $cleanedSql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                if (strpos($statement, 'INSERT INTO') !== false) {
                    preg_match('/INSERT INTO `?(\w+)`?/', $statement, $matches);
                    if (isset($matches[1])) {
                        echo "✓ Inserted data into: {$matches[1]}\n";
                    }
                }
            } catch (PDOException $e) {
                echo "⚠ Warning: " . $e->getMessage() . "\n";
            }
        }
    }

    echo "\n✅ Database reset completed successfully!\n";
    echo "\nYou can now run the installation: http://your-domain.com/install.php\n";

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
