<?php

namespace AstroGenix\Controllers;

use AstroGenix\Core\Controller;
use AstroGenix\Models\User;
use Exception;

/**
 * Authentication Controller
 */
class AuthController extends Controller
{
    public function login(): void
    {
        $this->requireGuest();
        
        if ($this->request->isPost()) {
            $this->handleLogin();
            return;
        }
        
        $data = [
            'page_title' => 'Login - AstroGenix',
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('auth/login', $data);
    }
    
    public function register(): void
    {
        $this->requireGuest();
        
        if ($this->request->isPost()) {
            $this->handleRegister();
            return;
        }
        
        $data = [
            'page_title' => 'Register - AstroGenix',
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('auth/register', $data);
    }
    
    public function logout(): void
    {
        $this->session->clear();
        $this->session->destroy();
        $this->session->setSuccess('You have been logged out successfully');
        $this->redirect('/');
    }
    
    public function forgotPassword(): void
    {
        $this->requireGuest();
        
        if ($this->request->isPost()) {
            $this->handleForgotPassword();
            return;
        }
        
        $data = [
            'page_title' => 'Forgot Password - AstroGenix',
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('auth/forgot-password', $data);
    }
    
    public function resetPassword(): void
    {
        $this->requireGuest();
        
        $token = $this->request->getParam('token');
        if (!$token) {
            $this->session->setError('Invalid reset token');
            $this->redirect('/forgot-password');
            return;
        }
        
        if ($this->request->isPost()) {
            $this->handleResetPassword($token);
            return;
        }
        
        $data = [
            'token' => $token,
            'page_title' => 'Reset Password - AstroGenix',
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('auth/reset-password', $data);
    }
    
    public function verifyEmail(): void
    {
        $token = $this->request->getParam('token');
        $userId = $this->request->getParam('user');
        
        if (!$token || !$userId) {
            $this->session->setError('Invalid verification link');
            $this->redirect('/');
            return;
        }
        
        $userModel = new User();
        $user = $userModel->find($userId);
        
        if (!$user) {
            $this->session->setError('User not found');
            $this->redirect('/');
            return;
        }
        
        // Verify token (in real implementation, store tokens in database)
        $expectedToken = hash('sha256', $user['email'] . $user['created_at']);
        
        if (!hash_equals($expectedToken, $token)) {
            $this->session->setError('Invalid verification token');
            $this->redirect('/');
            return;
        }
        
        if ($user['email_verified']) {
            $this->session->setInfo('Email already verified');
            $this->redirect('/login');
            return;
        }
        
        // Verify email
        $userModel->verifyEmail($userId);
        
        $this->session->setSuccess('Email verified successfully! You can now login.');
        $this->redirect('/login');
    }
    
    private function handleLogin(): void
    {
        $rules = [
            'email' => 'required|email',
            'password' => 'required|min:6',
            'csrf_token' => 'required'
        ];
        
        $validation = $this->validate($this->request->all(), $rules);
        
        if (!$validation['valid']) {
            $this->session->setError('Please correct the errors and try again');
            $this->redirect('/login');
            return;
        }
        
        if (!$this->validateCsrfToken($this->request->post('csrf_token'))) {
            $this->session->setError('Invalid security token');
            $this->redirect('/login');
            return;
        }
        
        $email = $this->request->post('email');
        $password = $this->request->post('password');
        $rememberMe = $this->request->post('remember_me') === '1';
        
        // Rate limiting
        $ip = $this->getClientIp();
        if (!$this->rateLimit('login_attempts_' . $ip, 10, 900)) { // 10 attempts per 15 minutes
            $this->session->setError('Too many login attempts. Please try again later.');
            $this->redirect('/login');
            return;
        }
        
        $userModel = new User();
        $user = $userModel->findByEmail($email);
        
        if (!$user) {
            $this->session->setError('Invalid email or password');
            $this->redirect('/login');
            return;
        }
        
        // Check if account is locked
        if ($userModel->isAccountLocked($user['id'])) {
            $this->session->setError('Account is temporarily locked due to multiple failed login attempts');
            $this->redirect('/login');
            return;
        }
        
        // Verify password
        if (!$userModel->verifyPassword($password, $user['password_hash'])) {
            $userModel->incrementLoginAttempts($user['id']);
            $this->session->setError('Invalid email or password');
            $this->redirect('/login');
            return;
        }
        
        // Check account status
        if ($user['status'] === 'suspended') {
            $this->session->setError('Your account has been suspended. Please contact support.');
            $this->redirect('/login');
            return;
        }
        
        if ($user['status'] === 'pending' && !$user['email_verified']) {
            $this->session->setError('Please verify your email address before logging in.');
            $this->redirect('/login');
            return;
        }
        
        // Successful login
        $userModel->updateLastLogin($user['id']);
        
        $this->session->set('user_id', $user['id']);
        $this->session->set('username', $user['username']);
        $this->session->regenerate();
        
        // Handle remember me
        if ($rememberMe) {
            $token = bin2hex(random_bytes(32));
            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true); // 30 days
            // In real implementation, store this token in database
        }
        
        $this->session->setSuccess('Welcome back, ' . $user['first_name'] . '!');
        
        // Redirect to intended page or dashboard
        $intended = $this->session->intended('/dashboard');
        $this->redirect($intended);
    }
    
    private function handleRegister(): void
    {
        $rules = [
            'first_name' => 'required|min:2|max:50',
            'last_name' => 'required|min:2|max:50',
            'username' => 'required|min:3|max:50|alpha_num',
            'email' => 'required|email',
            'password' => 'required|min:8',
            'password_confirm' => 'required|same:password',
            'country' => 'required',
            'terms' => 'required',
            'csrf_token' => 'required'
        ];
        
        $validation = $this->validate($this->request->all(), $rules);
        
        if (!$validation['valid']) {
            $this->session->setError('Please correct the errors and try again');
            $this->redirect('/register');
            return;
        }
        
        if (!$this->validateCsrfToken($this->request->post('csrf_token'))) {
            $this->session->setError('Invalid security token');
            $this->redirect('/register');
            return;
        }
        
        // Rate limiting
        $ip = $this->getClientIp();
        if (!$this->rateLimit('register_attempts_' . $ip, 3, 3600)) { // 3 attempts per hour
            $this->session->setError('Too many registration attempts. Please try again later.');
            $this->redirect('/register');
            return;
        }
        
        $data = $this->request->only([
            'first_name', 'last_name', 'username', 'email', 'password', 'country'
        ]);
        
        // Handle referral
        $referralCode = $this->request->post('referral_code');
        if ($referralCode) {
            $userModel = new User();
            $referrer = $userModel->findByReferralCode($referralCode);
            if ($referrer) {
                $data['referred_by'] = $referrer['id'];
            }
        }
        
        try {
            $userModel = new User();
            
            // Check if email already exists
            if ($userModel->findByEmail($data['email'])) {
                $this->session->setError('Email address is already registered');
                $this->redirect('/register');
                return;
            }
            
            // Check if username already exists
            if ($userModel->findByUsername($data['username'])) {
                $this->session->setError('Username is already taken');
                $this->redirect('/register');
                return;
            }
            
            // Create user
            $userId = $userModel->createUser($data);
            
            // Send verification email
            $user = $userModel->find($userId);
            $verificationToken = hash('sha256', $user['email'] . $user['created_at']);
            $verificationUrl = $this->request->getBaseUrl() . '/verify-email/' . $userId . '/' . $verificationToken;
            
            $this->sendEmail(
                $user['email'],
                'Verify Your Email - AstroGenix',
                'emails/verify-email',
                [
                    'user' => $user,
                    'verification_url' => $verificationUrl
                ]
            );
            
            $this->session->setSuccess('Registration successful! Please check your email to verify your account.');
            $this->redirect('/login');
            
        } catch (Exception $e) {
            $this->log('Registration error: ' . $e->getMessage(), 'error');
            $this->session->setError('Registration failed. Please try again.');
            $this->redirect('/register');
        }
    }
    
    private function handleForgotPassword(): void
    {
        $rules = [
            'email' => 'required|email',
            'csrf_token' => 'required'
        ];
        
        $validation = $this->validate($this->request->all(), $rules);
        
        if (!$validation['valid']) {
            $this->session->setError('Please enter a valid email address');
            $this->redirect('/forgot-password');
            return;
        }
        
        if (!$this->validateCsrfToken($this->request->post('csrf_token'))) {
            $this->session->setError('Invalid security token');
            $this->redirect('/forgot-password');
            return;
        }
        
        $email = $this->request->post('email');
        
        // Rate limiting
        if (!$this->rateLimit('forgot_password_' . $email, 3, 3600)) {
            $this->session->setError('Too many password reset requests. Please try again later.');
            $this->redirect('/forgot-password');
            return;
        }
        
        $userModel = new User();
        $user = $userModel->findByEmail($email);
        
        if ($user) {
            // Generate reset token
            $resetToken = bin2hex(random_bytes(32));
            $resetUrl = $this->request->getBaseUrl() . '/reset-password/' . $resetToken;
            
            // In real implementation, store reset token in database with expiration
            $this->cache('password_reset_' . $resetToken, $user['id'], 3600); // 1 hour
            
            // Send reset email
            $this->sendEmail(
                $user['email'],
                'Password Reset - AstroGenix',
                'emails/password-reset',
                [
                    'user' => $user,
                    'reset_url' => $resetUrl
                ]
            );
        }
        
        // Always show success message for security
        $this->session->setSuccess('If an account with that email exists, we have sent a password reset link.');
        $this->redirect('/forgot-password');
    }
    
    private function handleResetPassword(string $token): void
    {
        $rules = [
            'password' => 'required|min:8',
            'password_confirm' => 'required|same:password',
            'csrf_token' => 'required'
        ];
        
        $validation = $this->validate($this->request->all(), $rules);
        
        if (!$validation['valid']) {
            $this->session->setError('Please correct the errors and try again');
            $this->redirect('/reset-password/' . $token);
            return;
        }
        
        if (!$this->validateCsrfToken($this->request->post('csrf_token'))) {
            $this->session->setError('Invalid security token');
            $this->redirect('/reset-password/' . $token);
            return;
        }
        
        // Verify reset token
        $userId = $this->cache('password_reset_' . $token);
        if (!$userId) {
            $this->session->setError('Invalid or expired reset token');
            $this->redirect('/forgot-password');
            return;
        }
        
        $password = $this->request->post('password');
        
        try {
            $userModel = new User();
            $userModel->update($userId, [
                'password_hash' => password_hash($password, PASSWORD_DEFAULT)
            ]);
            
            // Clear reset token
            $this->cache('password_reset_' . $token, null);
            
            $this->session->setSuccess('Password reset successfully! You can now login with your new password.');
            $this->redirect('/login');
            
        } catch (Exception $e) {
            $this->log('Password reset error: ' . $e->getMessage(), 'error');
            $this->session->setError('Failed to reset password. Please try again.');
            $this->redirect('/reset-password/' . $token);
        }
    }
}
