<?php

namespace AstroGenix\Core;

/**
 * Simple File-based Logger Class
 */
class Logger
{
    private $logPath;
    private $logFile;
    
    public function __construct()
    {
        $this->logPath = storage_path('logs');
        $this->logFile = $this->logPath . '/astrogenix.log';
        
        // Create logs directory if it doesn't exist
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }
    
    public function log(string $level, string $message, array $context = []): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $contextString = !empty($context) ? ' ' . json_encode($context) : '';
        
        $logEntry = "[{$timestamp}] {$level}: {$message}{$contextString}" . PHP_EOL;
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    public function emergency(string $message, array $context = []): void
    {
        $this->log('EMERGENCY', $message, $context);
    }
    
    public function alert(string $message, array $context = []): void
    {
        $this->log('ALERT', $message, $context);
    }
    
    public function critical(string $message, array $context = []): void
    {
        $this->log('CRITICAL', $message, $context);
    }
    
    public function error(string $message, array $context = []): void
    {
        $this->log('ERROR', $message, $context);
    }
    
    public function warning(string $message, array $context = []): void
    {
        $this->log('WARNING', $message, $context);
    }
    
    public function notice(string $message, array $context = []): void
    {
        $this->log('NOTICE', $message, $context);
    }
    
    public function info(string $message, array $context = []): void
    {
        $this->log('INFO', $message, $context);
    }
    
    public function debug(string $message, array $context = []): void
    {
        $this->log('DEBUG', $message, $context);
    }
    
    public function getLogFile(): string
    {
        return $this->logFile;
    }
    
    public function setLogFile(string $file): void
    {
        $this->logFile = $file;
    }
    
    public function clearLog(): bool
    {
        return file_put_contents($this->logFile, '') !== false;
    }
    
    public function getLogSize(): int
    {
        return file_exists($this->logFile) ? filesize($this->logFile) : 0;
    }
    
    public function rotateLog(): bool
    {
        if (!file_exists($this->logFile)) {
            return true;
        }
        
        $timestamp = date('Y-m-d_H-i-s');
        $rotatedFile = $this->logPath . '/astrogenix_' . $timestamp . '.log';
        
        if (rename($this->logFile, $rotatedFile)) {
            touch($this->logFile);
            return true;
        }
        
        return false;
    }
}
