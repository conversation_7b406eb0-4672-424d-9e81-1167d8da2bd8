<?php

namespace AstroGenix\Models;

use AstroGenix\Core\Model;
use Exception;

/**
 * User Model
 */
class User extends Model
{
    protected $table = 'users';
    protected $fillable = [
        'username', 'email', 'password_hash', 'first_name', 'last_name',
        'phone', 'country', 'balance_usdt', 'total_invested', 'total_earned',
        'referral_code', 'referred_by', 'referral_earnings', 'status',
        'email_verified', 'two_factor_enabled', 'two_factor_secret',
        'last_login', 'login_attempts', 'locked_until'
    ];
    protected $hidden = ['password_hash', 'two_factor_secret'];
    
    public function createUser(array $userData): int
    {
        // Generate unique referral code
        $userData['referral_code'] = $this->generateReferralCode();
        
        // Hash password
        if (isset($userData['password'])) {
            $userData['password_hash'] = password_hash($userData['password'], PASSWORD_DEFAULT);
            unset($userData['password']);
        }
        
        return $this->create($userData);
    }
    
    public function findByEmail(string $email): ?array
    {
        return $this->findBy('email', $email);
    }
    
    public function findByUsername(string $username): ?array
    {
        return $this->findBy('username', $username);
    }
    
    public function findByReferralCode(string $code): ?array
    {
        return $this->findBy('referral_code', $code);
    }
    
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }
    
    public function updateBalance(int $userId, float $amount, string $type = 'add'): bool
    {
        $user = $this->find($userId);
        if (!$user) {
            return false;
        }
        
        $newBalance = $type === 'add' 
            ? $user['balance_usdt'] + $amount 
            : $user['balance_usdt'] - $amount;
            
        if ($newBalance < 0) {
            throw new Exception('Insufficient balance');
        }
        
        return $this->update($userId, ['balance_usdt' => $newBalance]);
    }
    
    public function updateTotalInvested(int $userId, float $amount): bool
    {
        $user = $this->find($userId);
        if (!$user) {
            return false;
        }
        
        $newTotal = $user['total_invested'] + $amount;
        return $this->update($userId, ['total_invested' => $newTotal]);
    }
    
    public function updateTotalEarned(int $userId, float $amount): bool
    {
        $user = $this->find($userId);
        if (!$user) {
            return false;
        }
        
        $newTotal = $user['total_earned'] + $amount;
        return $this->update($userId, ['total_earned' => $newTotal]);
    }
    
    public function updateReferralEarnings(int $userId, float $amount): bool
    {
        $user = $this->find($userId);
        if (!$user) {
            return false;
        }
        
        $newTotal = $user['referral_earnings'] + $amount;
        return $this->update($userId, ['referral_earnings' => $newTotal]);
    }
    
    public function getReferrals(int $userId, int $level = 1): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE referred_by = :user_id ORDER BY created_at DESC";
        $directReferrals = $this->db->fetchAll($sql, ['user_id' => $userId]);
        
        if ($level === 1) {
            return array_map([$this, 'hideFields'], $directReferrals);
        }
        
        // Get multi-level referrals
        $allReferrals = [];
        $currentLevel = $directReferrals;
        
        for ($i = 1; $i <= $level && !empty($currentLevel); $i++) {
            $allReferrals[$i] = array_map([$this, 'hideFields'], $currentLevel);
            
            if ($i < $level) {
                $userIds = array_column($currentLevel, 'id');
                if (!empty($userIds)) {
                    $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
                    $sql = "SELECT * FROM {$this->table} WHERE referred_by IN ({$placeholders}) ORDER BY created_at DESC";
                    $currentLevel = $this->db->fetchAll($sql, $userIds);
                } else {
                    $currentLevel = [];
                }
            }
        }
        
        return $allReferrals;
    }
    
    public function getReferralStats(int $userId): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_referrals,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_referrals,
                SUM(total_invested) as total_referral_investments
            FROM {$this->table} 
            WHERE referred_by = :user_id
        ";
        
        $stats = $this->db->fetch($sql, ['user_id' => $userId]);
        
        // Get referral earnings
        $earningsSql = "
            SELECT SUM(commission_amount) as total_earnings
            FROM referral_commissions 
            WHERE referrer_id = :user_id AND status = 'paid'
        ";
        
        $earnings = $this->db->fetch($earningsSql, ['user_id' => $userId]);
        $stats['total_earnings'] = $earnings['total_earnings'] ?? 0;
        
        return $stats;
    }
    
    public function updateLastLogin(int $userId): bool
    {
        return $this->update($userId, [
            'last_login' => date('Y-m-d H:i:s'),
            'login_attempts' => 0,
            'locked_until' => null
        ]);
    }
    
    public function incrementLoginAttempts(int $userId): bool
    {
        $user = $this->find($userId);
        if (!$user) {
            return false;
        }
        
        $attempts = $user['login_attempts'] + 1;
        $updateData = ['login_attempts' => $attempts];
        
        // Lock account after 5 failed attempts
        if ($attempts >= 5) {
            $updateData['locked_until'] = date('Y-m-d H:i:s', strtotime('+15 minutes'));
        }
        
        return $this->update($userId, $updateData);
    }
    
    public function isAccountLocked(int $userId): bool
    {
        $user = $this->find($userId);
        if (!$user || !$user['locked_until']) {
            return false;
        }
        
        return strtotime($user['locked_until']) > time();
    }
    
    public function verifyEmail(int $userId): bool
    {
        return $this->update($userId, ['email_verified' => 1]);
    }
    
    public function enableTwoFactor(int $userId, string $secret): bool
    {
        return $this->update($userId, [
            'two_factor_enabled' => 1,
            'two_factor_secret' => $secret
        ]);
    }
    
    public function disableTwoFactor(int $userId): bool
    {
        return $this->update($userId, [
            'two_factor_enabled' => 0,
            'two_factor_secret' => null
        ]);
    }
    
    private function generateReferralCode(): string
    {
        do {
            $code = 'AG' . strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
        } while ($this->findByReferralCode($code));
        
        return $code;
    }
    
    public function getTopInvestors(int $limit = 10): array
    {
        $sql = "
            SELECT username, total_invested, total_earned, created_at
            FROM {$this->table} 
            WHERE status = 'active' AND total_invested > 0
            ORDER BY total_invested DESC 
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, ['limit' => $limit]);
    }
    
    public function getUserStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_users,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_users,
                SUM(CASE WHEN status = 'suspended' THEN 1 ELSE 0 END) as suspended_users,
                SUM(balance_usdt) as total_balance,
                SUM(total_invested) as total_invested,
                SUM(total_earned) as total_earned,
                AVG(total_invested) as avg_investment
            FROM {$this->table}
        ";
        
        return $this->db->fetch($sql);
    }
}
