<?php
/**
 * AstroGenix Application Configuration
 */

return [
    'name' => $_ENV['APP_NAME'] ?? 'AstroGenix',
    'env' => $_ENV['APP_ENV'] ?? 'production',
    'debug' => $_ENV['APP_DEBUG'] ?? false,
    'url' => $_ENV['APP_URL'] ?? 'http://localhost',
    'timezone' => $_ENV['APP_TIMEZONE'] ?? 'UTC',
    
    'key' => $_ENV['APP_KEY'] ?? 'base64:' . base64_encode(random_bytes(32)),
    'cipher' => 'AES-256-CBC',
    
    'locale' => 'en',
    'fallback_locale' => 'en',
    'supported_locales' => ['en', 'ru'],
    
    'session' => [
        'lifetime' => 120, // minutes
        'expire_on_close' => false,
        'encrypt' => false,
        'files' => storage_path('sessions'),
        'connection' => null,
        'table' => 'sessions',
        'store' => null,
        'lottery' => [2, 100],
        'cookie' => 'astrogenix_session',
        'path' => '/',
        'domain' => null,
        'secure' => false,
        'http_only' => true,
        'same_site' => 'lax',
    ],
    
    'security' => [
        'password_min_length' => 8,
        'password_require_uppercase' => true,
        'password_require_lowercase' => true,
        'password_require_numbers' => true,
        'password_require_symbols' => false,
        'max_login_attempts' => 5,
        'lockout_duration' => 15, // minutes
        'csrf_token_lifetime' => 3600, // seconds
        'two_factor_issuer' => 'AstroGenix',
    ],
    
    'mail' => [
        'driver' => $_ENV['MAIL_DRIVER'] ?? 'smtp',
        'host' => $_ENV['MAIL_HOST'] ?? 'smtp.gmail.com',
        'port' => $_ENV['MAIL_PORT'] ?? 587,
        'username' => $_ENV['MAIL_USERNAME'] ?? '',
        'password' => $_ENV['MAIL_PASSWORD'] ?? '',
        'encryption' => $_ENV['MAIL_ENCRYPTION'] ?? 'tls',
        'from' => [
            'address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
            'name' => $_ENV['MAIL_FROM_NAME'] ?? 'AstroGenix',
        ],
    ],
    
    'upload' => [
        'max_file_size' => 5 * 1024 * 1024, // 5MB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf'],
        'upload_path' => 'uploads/',
        'screenshots_path' => 'uploads/screenshots/',
        'avatars_path' => 'uploads/avatars/',
        'news_images_path' => 'uploads/news/',
    ],
    
    'investment' => [
        'profit_distribution_time' => '00:00:00',
        'min_deposit' => 10.00,
        'min_withdrawal' => 5.00,
        'withdrawal_fee_percentage' => 2.0,
        'auto_approve_deposits' => false,
        'auto_approve_withdrawals' => false,
    ],
    
    'referral' => [
        'levels' => 3,
        'commissions' => [
            1 => 5.0, // Level 1: 5%
            2 => 3.0, // Level 2: 3%
            3 => 2.0, // Level 3: 2%
        ],
        'min_investment_for_commission' => 10.00,
    ],
    
    'api' => [
        'rate_limit' => 60, // requests per minute
        'rate_limit_window' => 60, // seconds
        'version' => 'v1',
    ],
    
    'cache' => [
        'default' => 'file',
        'stores' => [
            'file' => [
                'driver' => 'file',
                'path' => storage_path('cache'),
            ],
            'redis' => [
                'driver' => 'redis',
                'connection' => 'default',
            ],
        ],
        'prefix' => 'astrogenix_cache',
    ],
    
    'logging' => [
        'default' => 'daily',
        'channels' => [
            'daily' => [
                'driver' => 'daily',
                'path' => storage_path('logs/astrogenix.log'),
                'level' => 'debug',
                'days' => 14,
            ],
            'single' => [
                'driver' => 'single',
                'path' => storage_path('logs/astrogenix.log'),
                'level' => 'debug',
            ],
        ],
    ],
];
