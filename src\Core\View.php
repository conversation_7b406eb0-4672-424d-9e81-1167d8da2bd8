<?php

namespace AstroGenix\Core;

/**
 * View Rendering Class
 */
class View
{
    private $viewPath;
    private $data = [];
    
    public function __construct()
    {
        $this->viewPath = __DIR__ . '/../../views/';
    }
    
    public function render(string $template, array $data = []): void
    {
        $this->data = array_merge($this->data, $data);
        
        // Extract variables for use in template
        extract($this->data);
        
        // Start output buffering
        ob_start();
        
        // Include the template file
        $templateFile = $this->viewPath . str_replace('.', '/', $template) . '.php';
        
        if (!file_exists($templateFile)) {
            throw new \Exception("Template file not found: {$templateFile}");
        }
        
        include $templateFile;
        
        // Get the content and clean the buffer
        $content = ob_get_clean();
        
        // Output the content
        echo $content;
    }
    
    public function exists(string $template): bool
    {
        $templateFile = $this->viewPath . str_replace('.', '/', $template) . '.php';
        return file_exists($templateFile);
    }
    
    public function share(string $key, $value): void
    {
        $this->data[$key] = $value;
    }
    
    public function with(array $data): self
    {
        $this->data = array_merge($this->data, $data);
        return $this;
    }
    
    public function getData(): array
    {
        return $this->data;
    }
    
    public function setViewPath(string $path): void
    {
        $this->viewPath = rtrim($path, '/') . '/';
    }
    
    public function getViewPath(): string
    {
        return $this->viewPath;
    }
}
