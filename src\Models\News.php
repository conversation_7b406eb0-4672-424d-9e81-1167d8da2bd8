<?php

namespace AstroGenix\Models;

use AstroGenix\Core\Model;

/**
 * News Model
 */
class News extends Model
{
    protected $table = 'news';
    protected $fillable = [
        'category_id', 'title', 'slug', 'excerpt', 'content',
        'featured_image', 'status', 'published_at', 'author_id'
    ];
    
    public function getPublishedNews(array $conditions = [], int $page = 1, int $perPage = 12): array
    {
        $sql = "
            SELECT 
                n.*,
                nc.name as category_name,
                nc.slug as category_slug,
                au.full_name as author_name
            FROM {$this->table} n
            JOIN news_categories nc ON n.category_id = nc.id
            JOIN admin_users au ON n.author_id = au.id
            WHERE n.status = 'published' AND n.published_at <= NOW()
        ";
        
        $params = [];
        
        if (!empty($conditions)) {
            foreach ($conditions as $key => $value) {
                if ($key === 'category_slug') {
                    $sql .= " AND nc.slug = :category_slug";
                    $params['category_slug'] = $value;
                } else {
                    $sql .= " AND n.{$key} = :{$key}";
                    $params[$key] = $value;
                }
            }
        }
        
        $sql .= " ORDER BY n.published_at DESC";
        
        return $this->db->paginate($sql, $params, $page, $perPage);
    }
    
    public function getRecentNews(int $limit = 5): array
    {
        $sql = "
            SELECT 
                n.*,
                nc.name as category_name,
                nc.slug as category_slug
            FROM {$this->table} n
            JOIN news_categories nc ON n.category_id = nc.id
            WHERE n.status = 'published' AND n.published_at <= NOW()
            ORDER BY n.published_at DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, ['limit' => $limit]);
    }
    
    public function findBySlug(string $slug): ?array
    {
        $sql = "
            SELECT 
                n.*,
                nc.name as category_name,
                nc.slug as category_slug,
                au.full_name as author_name
            FROM {$this->table} n
            JOIN news_categories nc ON n.category_id = nc.id
            JOIN admin_users au ON n.author_id = au.id
            WHERE n.slug = :slug
            LIMIT 1
        ";
        
        return $this->db->fetch($sql, ['slug' => $slug]);
    }
    
    public function getRelatedArticles(int $categoryId, int $excludeId, int $limit = 3): array
    {
        $sql = "
            SELECT 
                n.*,
                nc.name as category_name
            FROM {$this->table} n
            JOIN news_categories nc ON n.category_id = nc.id
            WHERE n.category_id = :category_id 
            AND n.id != :exclude_id 
            AND n.status = 'published' 
            AND n.published_at <= NOW()
            ORDER BY n.published_at DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, [
            'category_id' => $categoryId,
            'exclude_id' => $excludeId,
            'limit' => $limit
        ]);
    }
    
    public function incrementViews(int $id): bool
    {
        $sql = "UPDATE {$this->table} SET views = views + 1 WHERE id = :id";
        $this->db->query($sql, ['id' => $id]);
        return true;
    }
    
    public function getCategories(): array
    {
        $sql = "
            SELECT 
                nc.*,
                COUNT(n.id) as article_count
            FROM news_categories nc
            LEFT JOIN {$this->table} n ON nc.id = n.category_id AND n.status = 'published'
            WHERE nc.status = 'active'
            GROUP BY nc.id
            ORDER BY nc.sort_order ASC
        ";
        
        return $this->db->fetchAll($sql);
    }
    
    public function getFaqArticles(): array
    {
        $sql = "
            SELECT n.*
            FROM {$this->table} n
            JOIN news_categories nc ON n.category_id = nc.id
            WHERE nc.slug = 'faq' AND n.status = 'published'
            ORDER BY n.created_at ASC
        ";
        
        return $this->db->fetchAll($sql);
    }
    
    public function createArticle(array $data): int
    {
        // Generate slug if not provided
        if (!isset($data['slug']) || empty($data['slug'])) {
            $data['slug'] = $this->generateSlug($data['title']);
        }
        
        // Set published_at if status is published and not set
        if ($data['status'] === 'published' && !isset($data['published_at'])) {
            $data['published_at'] = date('Y-m-d H:i:s');
        }
        
        return $this->create($data);
    }
    
    public function updateArticle(int $id, array $data): bool
    {
        // Update slug if title changed
        if (isset($data['title'])) {
            $current = $this->find($id);
            if ($current && $current['title'] !== $data['title']) {
                $data['slug'] = $this->generateSlug($data['title']);
            }
        }
        
        // Set published_at if status changed to published
        if (isset($data['status']) && $data['status'] === 'published') {
            $current = $this->find($id);
            if ($current && $current['status'] !== 'published') {
                $data['published_at'] = date('Y-m-d H:i:s');
            }
        }
        
        return $this->update($id, $data);
    }
    
    private function generateSlug(string $title): string
    {
        // Convert to lowercase and replace spaces with hyphens
        $slug = strtolower(trim($title));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // Ensure uniqueness
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->findBySlug($slug)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    public function getPopularArticles(int $limit = 5): array
    {
        $sql = "
            SELECT 
                n.*,
                nc.name as category_name
            FROM {$this->table} n
            JOIN news_categories nc ON n.category_id = nc.id
            WHERE n.status = 'published' AND n.published_at <= NOW()
            ORDER BY n.views DESC, n.published_at DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, ['limit' => $limit]);
    }
    
    public function searchArticles(string $query, int $page = 1, int $perPage = 12): array
    {
        $sql = "
            SELECT 
                n.*,
                nc.name as category_name,
                nc.slug as category_slug
            FROM {$this->table} n
            JOIN news_categories nc ON n.category_id = nc.id
            WHERE n.status = 'published' 
            AND n.published_at <= NOW()
            AND (n.title LIKE :query OR n.excerpt LIKE :query OR n.content LIKE :query)
            ORDER BY n.published_at DESC
        ";
        
        $params = ['query' => "%{$query}%"];
        
        return $this->db->paginate($sql, $params, $page, $perPage);
    }
    
    public function getArchive(): array
    {
        $sql = "
            SELECT 
                YEAR(published_at) as year,
                MONTH(published_at) as month,
                MONTHNAME(published_at) as month_name,
                COUNT(*) as article_count
            FROM {$this->table}
            WHERE status = 'published' AND published_at <= NOW()
            GROUP BY YEAR(published_at), MONTH(published_at)
            ORDER BY year DESC, month DESC
        ";
        
        return $this->db->fetchAll($sql);
    }
    
    public function getArticlesByDate(int $year, int $month, int $page = 1, int $perPage = 12): array
    {
        $sql = "
            SELECT 
                n.*,
                nc.name as category_name,
                nc.slug as category_slug
            FROM {$this->table} n
            JOIN news_categories nc ON n.category_id = nc.id
            WHERE n.status = 'published' 
            AND YEAR(n.published_at) = :year 
            AND MONTH(n.published_at) = :month
            ORDER BY n.published_at DESC
        ";
        
        $params = ['year' => $year, 'month' => $month];
        
        return $this->db->paginate($sql, $params, $page, $perPage);
    }
    
    public function getNewsStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_articles,
                SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published_articles,
                SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_articles,
                SUM(views) as total_views,
                AVG(views) as avg_views
            FROM {$this->table}
        ";
        
        return $this->db->fetch($sql) ?: [];
    }
}
