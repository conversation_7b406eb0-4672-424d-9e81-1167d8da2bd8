<?php
/**
 * AstroGenix - USDT Staking Platform
 * Main Entry Point
 */

// Проверяем, установлена ли платформа
if (!file_exists(__DIR__ . '/INSTALLED')) {
    // Если не установлена, перенаправляем на установку
    if (file_exists(__DIR__ . '/install.php')) {
        header('Location: install.php');
        exit;
    } else {
        die('Installation file not found. Please upload install.php');
    }
}

// Проверяем конфигурацию
if (!file_exists(__DIR__ . '/.env')) {
    die('Configuration file (.env) not found. Please run installation first.');
}

// Перенаправляем на public/index.php
require_once __DIR__ . '/public/index.php';
