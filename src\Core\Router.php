<?php

namespace AstroGenix\Core;

use Exception;

/**
 * Simple Router Class
 */
class Router
{
    private $routes = [];
    private $currentRoute = null;
    
    public function get(string $path, string $handler): void
    {
        $this->addRoute('GET', $path, $handler);
    }
    
    public function post(string $path, string $handler): void
    {
        $this->addRoute('POST', $path, $handler);
    }
    
    public function put(string $path, string $handler): void
    {
        $this->addRoute('PUT', $path, $handler);
    }
    
    public function delete(string $path, string $handler): void
    {
        $this->addRoute('DELETE', $path, $handler);
    }
    
    public function any(string $path, string $handler): void
    {
        $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];
        foreach ($methods as $method) {
            $this->addRoute($method, $path, $handler);
        }
    }
    
    private function addRoute(string $method, string $path, string $handler): void
    {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler,
            'pattern' => $this->convertToPattern($path)
        ];
    }
    
    private function convertToPattern(string $path): string
    {
        // Convert route parameters like {id} to regex patterns
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $path);
        $pattern = str_replace('/', '\/', $pattern);
        return '/^' . $pattern . '$/';
    }
    
    public function dispatch(): void
    {
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        $path = parse_url($_SERVER['REQUEST_URI'] ?? '/', PHP_URL_PATH);
        
        // Remove trailing slash except for root
        if ($path !== '/' && substr($path, -1) === '/') {
            $path = rtrim($path, '/');
        }
        
        $route = $this->findRoute($method, $path);
        
        if (!$route) {
            $this->handleNotFound();
            return;
        }
        
        $this->currentRoute = $route;
        $this->executeRoute($route);
    }
    
    private function findRoute(string $method, string $path): ?array
    {
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && preg_match($route['pattern'], $path, $matches)) {
                // Extract parameters
                $params = [];
                if (preg_match_all('/\{([^}]+)\}/', $route['path'], $paramNames)) {
                    for ($i = 1; $i < count($matches); $i++) {
                        $paramName = $paramNames[1][$i - 1];
                        $params[$paramName] = $matches[$i];
                    }
                }
                
                $route['params'] = $params;
                return $route;
            }
        }
        
        return null;
    }
    
    private function executeRoute(array $route): void
    {
        $handler = $route['handler'];
        $params = $route['params'] ?? [];
        
        if (is_string($handler)) {
            $this->executeStringHandler($handler, $params);
        } elseif (is_callable($handler)) {
            call_user_func($handler, $params);
        } else {
            throw new Exception('Invalid route handler');
        }
    }
    
    private function executeStringHandler(string $handler, array $params): void
    {
        if (strpos($handler, '@') === false) {
            throw new Exception('Invalid controller@method format');
        }
        
        list($controllerName, $methodName) = explode('@', $handler, 2);
        
        $controllerClass = 'AstroGenix\\Controllers\\' . $controllerName;
        
        if (!class_exists($controllerClass)) {
            throw new Exception("Controller {$controllerClass} not found");
        }
        
        $controller = new $controllerClass();
        
        if (!method_exists($controller, $methodName)) {
            throw new Exception("Method {$methodName} not found in {$controllerClass}");
        }
        
        // Set route parameters in request
        if (method_exists($controller, 'setRouteParams')) {
            $controller->setRouteParams($params);
        } elseif (property_exists($controller, 'request')) {
            $controller->request->setParams($params);
        }
        
        call_user_func([$controller, $methodName]);
    }
    
    private function handleNotFound(): void
    {
        http_response_code(404);
        
        // Try to load 404 page
        $notFoundPage = __DIR__ . '/../../views/errors/404.php';
        if (file_exists($notFoundPage)) {
            include $notFoundPage;
        } else {
            echo '<h1>404 - Page Not Found</h1>';
            echo '<p>The requested page could not be found.</p>';
        }
    }
    
    public function getCurrentRoute(): ?array
    {
        return $this->currentRoute;
    }
    
    public function url(string $name, array $params = []): string
    {
        // Simple URL generation - in real implementation, use named routes
        $url = $name;
        
        foreach ($params as $key => $value) {
            $url = str_replace('{' . $key . '}', $value, $url);
        }
        
        return url($url);
    }
    
    public function redirect(string $url, int $statusCode = 302): void
    {
        http_response_code($statusCode);
        header('Location: ' . $url);
        exit;
    }
    
    public function back(): void
    {
        $referer = $_SERVER['HTTP_REFERER'] ?? '/';
        $this->redirect($referer);
    }
    
    public function getRoutes(): array
    {
        return $this->routes;
    }
    
    public function group(array $attributes, callable $callback): void
    {
        $prefix = $attributes['prefix'] ?? '';
        $middleware = $attributes['middleware'] ?? [];
        
        // Store current state
        $originalRoutes = $this->routes;
        $this->routes = [];
        
        // Execute callback to register routes
        $callback($this);
        
        // Apply group attributes to new routes
        $groupRoutes = $this->routes;
        foreach ($groupRoutes as &$route) {
            if ($prefix) {
                $route['path'] = rtrim($prefix, '/') . '/' . ltrim($route['path'], '/');
                $route['pattern'] = $this->convertToPattern($route['path']);
            }
            
            if (!empty($middleware)) {
                $route['middleware'] = array_merge($route['middleware'] ?? [], $middleware);
            }
        }
        
        // Merge with original routes
        $this->routes = array_merge($originalRoutes, $groupRoutes);
    }
    
    public function middleware(array $middleware): self
    {
        // Simple middleware support
        foreach ($middleware as $middlewareClass) {
            if (class_exists($middlewareClass)) {
                $middlewareInstance = new $middlewareClass();
                if (method_exists($middlewareInstance, 'handle')) {
                    $middlewareInstance->handle();
                }
            }
        }
        
        return $this;
    }
    
    public function name(string $name): self
    {
        if (!empty($this->routes)) {
            $lastIndex = count($this->routes) - 1;
            $this->routes[$lastIndex]['name'] = $name;
        }
        
        return $this;
    }
    
    public function where(array $constraints): self
    {
        if (!empty($this->routes)) {
            $lastIndex = count($this->routes) - 1;
            $this->routes[$lastIndex]['constraints'] = $constraints;
            
            // Update pattern with constraints
            $path = $this->routes[$lastIndex]['path'];
            $pattern = $path;
            
            foreach ($constraints as $param => $constraint) {
                $pattern = str_replace('{' . $param . '}', '(' . $constraint . ')', $pattern);
            }
            
            $pattern = str_replace('/', '\/', $pattern);
            $this->routes[$lastIndex]['pattern'] = '/^' . $pattern . '$/';
        }
        
        return $this;
    }
}
