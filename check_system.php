<?php
/**
 * System Requirements Check for AstroGenix
 * Run this script before installation to verify system compatibility
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroGenix - Проверка системы</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .check-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .check-item.pass { background: #d4edda; color: #155724; }
        .check-item.fail { background: #f8d7da; color: #721c24; }
        .check-item.warning { background: #fff3cd; color: #856404; }
        .status { font-weight: bold; }
        .section { margin: 20px 0; }
        .section h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        .summary { margin-top: 30px; padding: 20px; border-radius: 5px; text-align: center; }
        .summary.ready { background: #d4edda; color: #155724; }
        .summary.not-ready { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AstroGenix</h1>
            <h2>Проверка системных требований</h2>
        </div>

        <?php
        $checks = [];
        $errors = 0;
        $warnings = 0;

        // PHP Version Check
        $phpVersion = PHP_VERSION;
        $phpVersionOk = version_compare($phpVersion, '7.4.0', '>=');
        $checks['PHP Version'] = [
            'status' => $phpVersionOk ? 'pass' : 'fail',
            'message' => "PHP $phpVersion " . ($phpVersionOk ? '✓' : '✗ (Требуется 7.4+)')
        ];
        if (!$phpVersionOk) $errors++;

        // Required PHP Extensions
        $requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'json', 'curl'];
        foreach ($requiredExtensions as $ext) {
            $loaded = extension_loaded($ext);
            $checks["PHP Extension: $ext"] = [
                'status' => $loaded ? 'pass' : 'fail',
                'message' => $loaded ? '✓ Загружено' : '✗ Не найдено'
            ];
            if (!$loaded) $errors++;
        }

        // Optional PHP Extensions
        $optionalExtensions = ['gd', 'zip', 'xml'];
        foreach ($optionalExtensions as $ext) {
            $loaded = extension_loaded($ext);
            $checks["PHP Extension: $ext (опционально)"] = [
                'status' => $loaded ? 'pass' : 'warning',
                'message' => $loaded ? '✓ Загружено' : '⚠ Рекомендуется'
            ];
            if (!$loaded) $warnings++;
        }

        // Directory Permissions
        $directories = [
            'storage' => 'storage/',
            'uploads' => 'public/uploads/',
            'cache' => 'storage/cache/',
            'logs' => 'storage/logs/',
            'sessions' => 'storage/sessions/'
        ];

        foreach ($directories as $name => $dir) {
            if (!is_dir($dir)) {
                @mkdir($dir, 0755, true);
            }
            
            $writable = is_writable($dir);
            $checks["Права записи: $name"] = [
                'status' => $writable ? 'pass' : 'fail',
                'message' => $writable ? '✓ Доступна запись' : '✗ Нет прав записи'
            ];
            if (!$writable) $errors++;
        }

        // Database Files Check
        $dbFiles = ['database/schema.sql', 'database/seed_data.sql'];
        foreach ($dbFiles as $file) {
            $exists = file_exists($file);
            $checks["Файл БД: " . basename($file)] = [
                'status' => $exists ? 'pass' : 'fail',
                'message' => $exists ? '✓ Найден' : '✗ Не найден'
            ];
            if (!$exists) $errors++;
        }

        // Memory Limit
        $memoryLimit = ini_get('memory_limit');
        $memoryBytes = return_bytes($memoryLimit);
        $memoryOk = $memoryBytes >= 128 * 1024 * 1024; // 128MB
        $checks['Лимит памяти PHP'] = [
            'status' => $memoryOk ? 'pass' : 'warning',
            'message' => "$memoryLimit " . ($memoryOk ? '✓' : '⚠ Рекомендуется 128M+')
        ];
        if (!$memoryOk) $warnings++;

        // Max Execution Time
        $maxTime = ini_get('max_execution_time');
        $timeOk = $maxTime == 0 || $maxTime >= 60;
        $checks['Время выполнения PHP'] = [
            'status' => $timeOk ? 'pass' : 'warning',
            'message' => ($maxTime == 0 ? 'Без ограничений' : $maxTime . 's') . ($timeOk ? ' ✓' : ' ⚠ Рекомендуется 60s+')
        ];
        if (!$timeOk) $warnings++;

        function return_bytes($val) {
            $val = trim($val);
            $last = strtolower($val[strlen($val)-1]);
            $val = (int) $val;
            switch($last) {
                case 'g': $val *= 1024;
                case 'm': $val *= 1024;
                case 'k': $val *= 1024;
            }
            return $val;
        }
        ?>

        <div class="section">
            <h3>Результаты проверки</h3>
            <?php foreach ($checks as $name => $check): ?>
                <div class="check-item <?= $check['status'] ?>">
                    <span><?= $name ?></span>
                    <span class="status"><?= $check['message'] ?></span>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="summary <?= $errors > 0 ? 'not-ready' : 'ready' ?>">
            <?php if ($errors > 0): ?>
                <h3>❌ Система не готова к установке</h3>
                <p>Найдено <?= $errors ?> критических ошибок и <?= $warnings ?> предупреждений.</p>
                <p>Пожалуйста, исправьте все ошибки перед установкой.</p>
            <?php else: ?>
                <h3>✅ Система готова к установке</h3>
                <p>Все требования выполнены! <?php if ($warnings > 0): ?>Найдено <?= $warnings ?> предупреждений.<?php endif; ?></p>
                <p><a href="install.php" style="color: #155724; font-weight: bold;">Перейти к установке →</a></p>
            <?php endif; ?>
        </div>

        <div class="section">
            <h3>Дополнительная информация</h3>
            <div class="check-item pass">
                <span>Версия PHP</span>
                <span><?= PHP_VERSION ?></span>
            </div>
            <div class="check-item pass">
                <span>Веб-сервер</span>
                <span><?= $_SERVER['SERVER_SOFTWARE'] ?? 'Неизвестно' ?></span>
            </div>
            <div class="check-item pass">
                <span>Операционная система</span>
                <span><?= PHP_OS ?></span>
            </div>
            <div class="check-item pass">
                <span>Текущая директория</span>
                <span><?= __DIR__ ?></span>
            </div>
        </div>
    </div>
</body>
</html>
