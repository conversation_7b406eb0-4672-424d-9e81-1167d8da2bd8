# AstroGenix Environment Configuration
# Copy this file to .env and update the values

# Application
APP_NAME=AstroGenix
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost
APP_KEY=
APP_TIMEZONE=UTC

# Database
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=astrogenix
DB_USERNAME=root
DB_PASSWORD=

# Redis (Optional - for caching and sessions)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Mail Configuration
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=AstroGenix

# Security
JWT_SECRET=your-jwt-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here

# Payment Settings
USDT_WALLET_ADDRESS=TQn9Y2khEsLJW1ChVWFMSMeRDow5oNDMnt
MIN_DEPOSIT=10.00
MIN_WITHDRAWAL=5.00
WITHDRAWAL_FEE_PERCENTAGE=2.0

# Referral Settings
REFERRAL_COMMISSION_L1=5.0
REFERRAL_COMMISSION_L2=3.0
REFERRAL_COMMISSION_L3=2.0

# API Settings
API_RATE_LIMIT=60
API_VERSION=v1

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# External APIs (if needed)
COINGECKO_API_KEY=
BLOCKCHAIN_API_KEY=

# Social Media Links
TELEGRAM_URL=https://t.me/astrogenix
TWITTER_URL=https://twitter.com/astrogenix
FACEBOOK_URL=https://facebook.com/astrogenix
INSTAGRAM_URL=https://instagram.com/astrogenix

# Company Information
COMPANY_NAME=AstroGenix Ltd.
COMPANY_ADDRESS=123 Crypto Street, Blockchain City, BC 12345
COMPANY_PHONE=******-ASTRO-GX
COMPANY_EMAIL=<EMAIL>
COMPANY_REGISTRATION=AG-2025-001

# Support
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=******-ASTRO-GX
SUPPORT_HOURS=24/7
