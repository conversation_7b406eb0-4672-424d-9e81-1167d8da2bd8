/**
 * AstroGenix - Main JavaScript Application
 */

(function($) {
    'use strict';

    // Global App Object
    window.AstroGenix = {
        init: function() {
            this.initLoader();
            this.initNavigation();
            this.initScrollEffects();
            this.initAnimations();
            this.initForms();
            this.initTooltips();
            this.initModals();
            this.initCharts();
            this.initCounters();
            this.initCopyToClipboard();
            this.initTheme();
        },

        // Loading Spinner
        initLoader: function() {
            $(window).on('load', function() {
                $('#loading-spinner').fadeOut(500);
            });

            // Show loader on form submissions
            $('form').on('submit', function() {
                $('#loading-spinner').addClass('show');
            });
        },

        // Navigation
        initNavigation: function() {
            // Navbar scroll effect
            $(window).scroll(function() {
                if ($(this).scrollTop() > 50) {
                    $('.navbar').addClass('scrolled');
                } else {
                    $('.navbar').removeClass('scrolled');
                }
            });

            // Smooth scrolling for anchor links
            $('a[href^="#"]').on('click', function(e) {
                e.preventDefault();
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 80
                    }, 1000);
                }
            });

            // Mobile menu close on link click
            $('.navbar-nav .nav-link').on('click', function() {
                $('.navbar-collapse').collapse('hide');
            });
        },

        // Scroll Effects
        initScrollEffects: function() {
            // Back to top button
            $(window).scroll(function() {
                if ($(this).scrollTop() > 300) {
                    $('.back-to-top').fadeIn();
                } else {
                    $('.back-to-top').fadeOut();
                }
            });

            $('.back-to-top').on('click', function(e) {
                e.preventDefault();
                $('html, body').animate({scrollTop: 0}, 1000);
            });

            // Parallax effect for hero section
            $(window).scroll(function() {
                var scrolled = $(this).scrollTop();
                var parallax = $('.hero-particles');
                var speed = scrolled * 0.5;
                parallax.css('transform', 'translateY(' + speed + 'px)');
            });
        },

        // Animations
        initAnimations: function() {
            // Intersection Observer for fade-in animations
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');
                        }
                    });
                });

                // Observe elements with animation classes
                document.querySelectorAll('.feature-card, .plan-card, .news-card').forEach(el => {
                    observer.observe(el);
                });
            }

            // Typing effect for hero title
            this.initTypingEffect();
        },

        // Typing Effect
        initTypingEffect: function() {
            const heroTitle = document.querySelector('.hero-title');
            if (heroTitle && heroTitle.dataset.typing) {
                const text = heroTitle.textContent;
                heroTitle.textContent = '';
                let i = 0;
                
                function typeWriter() {
                    if (i < text.length) {
                        heroTitle.textContent += text.charAt(i);
                        i++;
                        setTimeout(typeWriter, 50);
                    }
                }
                
                setTimeout(typeWriter, 1000);
            }
        },

        // Forms
        initForms: function() {
            // Form validation
            $('form').each(function() {
                $(this).on('submit', function(e) {
                    var isValid = true;
                    
                    // Check required fields
                    $(this).find('[required]').each(function() {
                        if (!$(this).val()) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        } else {
                            $(this).removeClass('is-invalid');
                        }
                    });

                    // Email validation
                    $(this).find('input[type="email"]').each(function() {
                        var email = $(this).val();
                        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (email && !emailRegex.test(email)) {
                            isValid = false;
                            $(this).addClass('is-invalid');
                        }
                    });

                    // Password confirmation
                    var password = $(this).find('input[name="password"]').val();
                    var confirmPassword = $(this).find('input[name="password_confirm"]').val();
                    if (password && confirmPassword && password !== confirmPassword) {
                        isValid = false;
                        $(this).find('input[name="password_confirm"]').addClass('is-invalid');
                    }

                    if (!isValid) {
                        e.preventDefault();
                        this.showAlert('Please correct the errors and try again.', 'danger');
                    }
                });
            });

            // Real-time validation
            $('input, textarea, select').on('blur', function() {
                if ($(this).attr('required') && !$(this).val()) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // Password strength indicator
            $('input[name="password"]').on('input', function() {
                var password = $(this).val();
                var strength = this.calculatePasswordStrength(password);
                this.updatePasswordStrength(strength);
            }.bind(this));
        },

        // Password Strength
        calculatePasswordStrength: function(password) {
            var score = 0;
            if (password.length >= 8) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/[0-9]/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;
            return score;
        },

        updatePasswordStrength: function(strength) {
            var indicator = $('.password-strength');
            if (indicator.length) {
                var classes = ['weak', 'fair', 'good', 'strong', 'very-strong'];
                var labels = ['Weak', 'Fair', 'Good', 'Strong', 'Very Strong'];
                
                indicator.removeClass(classes.join(' '));
                if (strength > 0) {
                    indicator.addClass(classes[strength - 1]);
                    indicator.text(labels[strength - 1]);
                }
            }
        },

        // Tooltips and Popovers
        initTooltips: function() {
            // Initialize Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize Bootstrap popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function(popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        },

        // Modals
        initModals: function() {
            // Auto-focus first input in modals
            $('.modal').on('shown.bs.modal', function() {
                $(this).find('input:first').focus();
            });

            // Confirm dialogs
            $('[data-confirm]').on('click', function(e) {
                var message = $(this).data('confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        },

        // Charts (if Chart.js is loaded)
        initCharts: function() {
            if (typeof Chart !== 'undefined') {
                // Investment chart
                var investmentChart = document.getElementById('investmentChart');
                if (investmentChart) {
                    this.createInvestmentChart(investmentChart);
                }

                // Profit chart
                var profitChart = document.getElementById('profitChart');
                if (profitChart) {
                    this.createProfitChart(profitChart);
                }
            }
        },

        // Counters
        initCounters: function() {
            $('.counter').each(function() {
                var $this = $(this);
                var countTo = $this.attr('data-count');
                
                $({ countNum: $this.text() }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'linear',
                    step: function() {
                        $this.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $this.text(this.countNum);
                    }
                });
            });
        },

        // Copy to Clipboard
        initCopyToClipboard: function() {
            $('.copy-btn').on('click', function() {
                var target = $(this).data('target');
                var text = $(target).text() || $(target).val();
                
                navigator.clipboard.writeText(text).then(function() {
                    AstroGenix.showAlert('Copied to clipboard!', 'success');
                }).catch(function() {
                    AstroGenix.showAlert('Failed to copy to clipboard.', 'danger');
                });
            });
        },

        // Theme Management
        initTheme: function() {
            // Dark mode toggle
            $('.theme-toggle').on('click', function() {
                $('body').toggleClass('dark-theme');
                localStorage.setItem('theme', $('body').hasClass('dark-theme') ? 'dark' : 'light');
            });

            // Load saved theme
            var savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                $('body').addClass('dark-theme');
            }
        },

        // Utility Functions
        showAlert: function(message, type = 'info') {
            var alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('.alert-container').html(alertHtml);
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        },

        formatCurrency: function(amount, currency = 'USDT') {
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 6
            }).format(amount) + ' ' + currency;
        },

        formatDate: function(date) {
            return new Intl.DateTimeFormat('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            }).format(new Date(date));
        },

        // AJAX Helper
        ajax: function(url, data = {}, method = 'POST') {
            return $.ajax({
                url: url,
                method: method,
                data: data,
                dataType: 'json',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
        },

        // Investment Calculator
        calculateInvestment: function(packageId, amount) {
            return this.ajax('/api/calculate', {
                package_id: packageId,
                amount: amount
            });
        },

        // Real-time Updates
        initRealTimeUpdates: function() {
            // Update stats every 30 seconds
            setInterval(function() {
                AstroGenix.ajax('/api/stats', {}, 'GET').done(function(response) {
                    if (response.success) {
                        $('.stat-users').text(response.data.total_users);
                        $('.stat-invested').text('$' + response.data.total_invested.toLocaleString());
                        $('.stat-earned').text('$' + response.data.total_earned.toLocaleString());
                    }
                });
            }, 30000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        AstroGenix.init();
    });

    // Additional initialization after page load
    $(window).on('load', function() {
        AstroGenix.initCounters();
        AstroGenix.initRealTimeUpdates();
    });

})(jQuery);
