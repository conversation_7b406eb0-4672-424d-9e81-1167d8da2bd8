<?php

namespace AstroGenix\Core;

use Exception;

/**
 * Base Model Class
 */
abstract class Model
{
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    protected $timestamps = true;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    public function find(int $id): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
        $result = $this->db->fetch($sql, ['id' => $id]);
        
        if ($result) {
            return $this->hideFields($result);
        }
        
        return null;
    }
    
    public function findBy(string $column, $value): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} = :value LIMIT 1";
        $result = $this->db->fetch($sql, ['value' => $value]);
        
        if ($result) {
            return $this->hideFields($result);
        }
        
        return null;
    }
    
    public function findAll(array $conditions = [], string $orderBy = '', int $limit = 0): array
    {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereParts = [];
            foreach ($conditions as $column => $value) {
                $whereParts[] = "{$column} = :{$column}";
                $params[$column] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereParts);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit > 0) {
            $sql .= " LIMIT {$limit}";
        }
        
        $results = $this->db->fetchAll($sql, $params);
        
        return array_map([$this, 'hideFields'], $results);
    }
    
    public function create(array $data): int
    {
        $data = $this->filterFillable($data);
        
        if ($this->timestamps) {
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        return $this->db->insert($this->table, $data);
    }
    
    public function update(int $id, array $data): bool
    {
        $data = $this->filterFillable($data);
        
        if ($this->timestamps) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $rowsAffected = $this->db->update(
            $this->table,
            $data,
            "{$this->primaryKey} = :id",
            ['id' => $id]
        );
        
        return $rowsAffected > 0;
    }
    
    public function delete(int $id): bool
    {
        $rowsAffected = $this->db->delete(
            $this->table,
            "{$this->primaryKey} = :id",
            ['id' => $id]
        );
        
        return $rowsAffected > 0;
    }
    
    public function exists(int $id): bool
    {
        return $this->db->exists($this->table, "{$this->primaryKey} = :id", ['id' => $id]);
    }
    
    public function count(array $conditions = []): int
    {
        $where = '1=1';
        $params = [];
        
        if (!empty($conditions)) {
            $whereParts = [];
            foreach ($conditions as $column => $value) {
                $whereParts[] = "{$column} = :{$column}";
                $params[$column] = $value;
            }
            $where = implode(' AND ', $whereParts);
        }
        
        return $this->db->count($this->table, $where, $params);
    }
    
    public function paginate(array $conditions = [], int $page = 1, int $perPage = 20, string $orderBy = ''): array
    {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereParts = [];
            foreach ($conditions as $column => $value) {
                $whereParts[] = "{$column} = :{$column}";
                $params[$column] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereParts);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        $result = $this->db->paginate($sql, $params, $page, $perPage);
        $result['data'] = array_map([$this, 'hideFields'], $result['data']);
        
        return $result;
    }
    
    public function search(string $query, array $searchFields, array $conditions = [], int $page = 1, int $perPage = 20): array
    {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];
        $whereParts = [];
        
        // Add search conditions
        if (!empty($query) && !empty($searchFields)) {
            $searchParts = [];
            foreach ($searchFields as $field) {
                $searchParts[] = "{$field} LIKE :search";
            }
            $whereParts[] = "(" . implode(' OR ', $searchParts) . ")";
            $params['search'] = "%{$query}%";
        }
        
        // Add additional conditions
        if (!empty($conditions)) {
            foreach ($conditions as $column => $value) {
                $whereParts[] = "{$column} = :{$column}";
                $params[$column] = $value;
            }
        }
        
        if (!empty($whereParts)) {
            $sql .= " WHERE " . implode(' AND ', $whereParts);
        }
        
        $sql .= " ORDER BY {$this->primaryKey} DESC";
        
        $result = $this->db->paginate($sql, $params, $page, $perPage);
        $result['data'] = array_map([$this, 'hideFields'], $result['data']);
        
        return $result;
    }
    
    protected function filterFillable(array $data): array
    {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }
    
    protected function hideFields(array $data): array
    {
        if (empty($this->hidden)) {
            return $data;
        }
        
        return array_diff_key($data, array_flip($this->hidden));
    }
    
    public function raw(string $sql, array $params = []): array
    {
        return $this->db->fetchAll($sql, $params);
    }
    
    public function rawSingle(string $sql, array $params = []): ?array
    {
        return $this->db->fetch($sql, $params);
    }
    
    public function getTable(): string
    {
        return $this->table;
    }
    
    public function getPrimaryKey(): string
    {
        return $this->primaryKey;
    }
}
