<?php

namespace AstroGenix\Core;

/**
 * Simple File-based Cache Class
 */
class Cache
{
    private $cachePath;
    private $defaultTtl = 3600; // 1 hour
    
    public function __construct()
    {
        $this->cachePath = storage_path('cache');
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cachePath)) {
            mkdir($this->cachePath, 0755, true);
        }
    }
    
    public function get(string $key, $default = null)
    {
        $filePath = $this->getFilePath($key);
        
        if (!file_exists($filePath)) {
            return $default;
        }
        
        $data = file_get_contents($filePath);
        $data = unserialize($data);
        
        // Check if expired
        if ($data['expires'] < time()) {
            $this->forget($key);
            return $default;
        }
        
        return $data['value'];
    }
    
    public function set(string $key, $value, int $ttl = null): bool
    {
        $ttl = $ttl ?? $this->defaultTtl;
        $filePath = $this->getFilePath($key);
        
        $data = [
            'value' => $value,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        $serialized = serialize($data);
        
        return file_put_contents($filePath, $serialized, LOCK_EX) !== false;
    }
    
    public function forget(string $key): bool
    {
        $filePath = $this->getFilePath($key);
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return true;
    }
    
    public function flush(): bool
    {
        $files = glob($this->cachePath . '/*');
        
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        
        return true;
    }
    
    public function has(string $key): bool
    {
        return $this->get($key) !== null;
    }
    
    public function remember(string $key, callable $callback, int $ttl = null)
    {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }
        
        $value = $callback();
        $this->set($key, $value, $ttl);
        
        return $value;
    }
    
    public function increment(string $key, int $value = 1): int
    {
        $current = (int) $this->get($key, 0);
        $new = $current + $value;
        $this->set($key, $new);
        
        return $new;
    }
    
    public function decrement(string $key, int $value = 1): int
    {
        $current = (int) $this->get($key, 0);
        $new = $current - $value;
        $this->set($key, $new);
        
        return $new;
    }
    
    public function pull(string $key, $default = null)
    {
        $value = $this->get($key, $default);
        $this->forget($key);
        
        return $value;
    }
    
    public function forever(string $key, $value): bool
    {
        return $this->set($key, $value, 315360000); // 10 years
    }
    
    public function many(array $keys): array
    {
        $result = [];
        
        foreach ($keys as $key) {
            $result[$key] = $this->get($key);
        }
        
        return $result;
    }
    
    public function putMany(array $values, int $ttl = null): bool
    {
        foreach ($values as $key => $value) {
            if (!$this->set($key, $value, $ttl)) {
                return false;
            }
        }
        
        return true;
    }
    
    public function getMultiple(array $keys, $default = null): array
    {
        $result = [];
        
        foreach ($keys as $key) {
            $result[$key] = $this->get($key, $default);
        }
        
        return $result;
    }
    
    public function setMultiple(array $values, int $ttl = null): bool
    {
        return $this->putMany($values, $ttl);
    }
    
    public function deleteMultiple(array $keys): bool
    {
        foreach ($keys as $key) {
            if (!$this->forget($key)) {
                return false;
            }
        }
        
        return true;
    }
    
    public function clear(): bool
    {
        return $this->flush();
    }
    
    public function getStats(): array
    {
        $files = glob($this->cachePath . '/*');
        $totalSize = 0;
        $totalFiles = 0;
        $expired = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $totalFiles++;
                $totalSize += filesize($file);
                
                // Check if expired
                $data = file_get_contents($file);
                $data = unserialize($data);
                
                if ($data['expires'] < time()) {
                    $expired++;
                }
            }
        }
        
        return [
            'total_files' => $totalFiles,
            'total_size' => $totalSize,
            'expired_files' => $expired,
            'cache_path' => $this->cachePath
        ];
    }
    
    public function cleanup(): int
    {
        $files = glob($this->cachePath . '/*');
        $cleaned = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $data = file_get_contents($file);
                $data = unserialize($data);
                
                if ($data['expires'] < time()) {
                    unlink($file);
                    $cleaned++;
                }
            }
        }
        
        return $cleaned;
    }
    
    private function getFilePath(string $key): string
    {
        $hash = md5($key);
        return $this->cachePath . '/' . $hash . '.cache';
    }
    
    private function sanitizeKey(string $key): string
    {
        return preg_replace('/[^a-zA-Z0-9_\-]/', '_', $key);
    }
}
