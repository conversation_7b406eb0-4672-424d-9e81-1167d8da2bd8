# AstroGenix - Professional USDT Staking Platform

AstroGenix is a modern, secure, and feature-rich USDT staking platform built with PHP. It offers multiple investment packages, referral programs, real-time profit distribution, and comprehensive admin management tools.

## 🚀 Features

### User Features
- **Multiple Investment Packages**: 5 different plans with varying returns (1.2% - 2.5% daily)
- **Real-time Profit Distribution**: Daily profits automatically credited to user accounts
- **Multi-level Referral System**: Earn commissions up to 3 levels (5%, 3%, 2%)
- **Secure Transactions**: Deposit and withdrawal management with admin approval
- **Task System**: Complete tasks to earn bonus rewards
- **Support Tickets**: Built-in customer support system
- **Two-Factor Authentication**: Enhanced account security
- **Mobile Responsive**: Fully optimized for all devices

### Admin Features
- **Comprehensive Dashboard**: Real-time statistics and analytics
- **User Management**: Complete user account control
- **Investment Management**: Monitor and manage all investments
- **Transaction Control**: Approve/reject deposits and withdrawals
- **News Management**: Create and manage platform news
- **Package Management**: Create and modify investment packages
- **Support System**: Handle customer support tickets
- **System Settings**: Configure platform parameters

### Technical Features
- **Modern PHP Architecture**: Clean, maintainable code structure
- **Secure Database Design**: Properly normalized MySQL database
- **Advanced Security**: CSRF protection, SQL injection prevention, XSS protection
- **Responsive Design**: Bootstrap 5 with custom styling
- **SEO Optimized**: Proper meta tags and structured data
- **Multi-language Support**: English and Russian languages
- **Caching System**: Improved performance with caching
- **Email Integration**: Automated email notifications

## 📋 Requirements

- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher
- **Apache/Nginx**: Web server with mod_rewrite
- **Extensions**: PDO, MySQLi, OpenSSL, cURL, GD, Mbstring
- **Composer**: For dependency management (optional)

## 🛠️ Installation

### 1. Download and Extract
```bash
# Download the project files
# Extract to your web server directory (e.g., /var/www/html/astrogenix)
```

### 2. Database Setup
```sql
-- Create database
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import schema
mysql -u username -p astrogenix < database/schema.sql

-- Import initial data
mysql -u username -p astrogenix < database/seed_data.sql
```

### 3. Configuration
```bash
# Copy environment file
cp .env.example .env

# Edit configuration
nano .env
```

Update the following settings in `.env`:
```env
# Database
DB_HOST=localhost
DB_DATABASE=astrogenix
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Application
APP_URL=https://yourdomain.com
APP_KEY=your-secret-key-here

# Mail Settings
MAIL_HOST=smtp.gmail.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# USDT Wallet
USDT_WALLET_ADDRESS=your-usdt-wallet-address
```

### 4. File Permissions
```bash
# Set proper permissions
chmod 755 public/
chmod -R 777 storage/
chmod -R 777 public/uploads/
chmod 644 .env
```

### 5. Web Server Configuration

#### Apache
The `.htaccess` file is already configured. Ensure `mod_rewrite` is enabled:
```bash
sudo a2enmod rewrite
sudo systemctl restart apache2
```

#### Nginx
Add this configuration to your Nginx server block:
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
    include fastcgi_params;
}
```

## 🔧 Configuration

### Admin Account
Default admin credentials:
- **Username**: admin
- **Email**: <EMAIL>
- **Password**: admin123

**⚠️ Important**: Change the default admin password immediately after installation!

### Investment Packages
The platform comes with 5 pre-configured investment packages:
1. **Starter Plan**: $10-$999, 1.2% daily, 30 days
2. **Growth Plan**: $1,000-$4,999, 1.5% daily, 45 days
3. **Premium Plan**: $5,000-$19,999, 1.8% daily, 60 days
4. **Elite Plan**: $20,000-$99,999, 2.2% daily, 90 days
5. **VIP Plan**: $100,000+, 2.5% daily, 120 days

### Cron Jobs
Set up the following cron jobs for automated tasks:

```bash
# Daily profit distribution (runs every day at midnight)
0 0 * * * /usr/bin/php /path/to/your/project/cron/distribute_profits.php

# Process pending transactions (runs every hour)
0 * * * * /usr/bin/php /path/to/your/project/cron/process_transactions.php

# Send email notifications (runs every 15 minutes)
*/15 * * * * /usr/bin/php /path/to/your/project/cron/send_notifications.php

# Clean up expired sessions (runs daily at 2 AM)
0 2 * * * /usr/bin/php /path/to/your/project/cron/cleanup_sessions.php
```

## 🔒 Security

### Security Features
- **CSRF Protection**: All forms protected against CSRF attacks
- **SQL Injection Prevention**: Prepared statements used throughout
- **XSS Protection**: Input sanitization and output escaping
- **Password Hashing**: Secure password storage with PHP's password_hash()
- **Session Security**: Secure session configuration
- **Rate Limiting**: Protection against brute force attacks
- **File Upload Security**: Restricted file types and validation

### Security Headers
The platform includes comprehensive security headers:
- Content Security Policy (CSP)
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection
- Strict-Transport-Security (HSTS)

### Recommended Security Measures
1. **SSL Certificate**: Always use HTTPS in production
2. **Regular Updates**: Keep PHP and MySQL updated
3. **Backup Strategy**: Implement regular database backups
4. **Monitoring**: Set up server monitoring and logging
5. **Firewall**: Configure server firewall rules

## 📱 API Documentation

### Public Endpoints
```
GET /api/stats - Platform statistics
GET /api/packages - Investment packages
POST /api/calculate - Investment calculator
```

### Authentication Required
```
GET /api/user/profile - User profile
GET /api/user/investments - User investments
GET /api/user/transactions - User transactions
POST /api/user/invest - Create investment
```

## 🎨 Customization

### Themes
The platform supports custom themes. To create a new theme:
1. Copy `public/assets/css/style.css` to `public/assets/css/themes/your-theme.css`
2. Modify colors and styles as needed
3. Update the theme selector in the admin panel

### Languages
To add a new language:
1. Create language files in `resources/lang/your-language/`
2. Update the language selector in the navigation
3. Add translation keys to the translation helper

### Email Templates
Email templates are located in `views/emails/`. Customize them to match your brand:
- `verify-email.php` - Email verification
- `password-reset.php` - Password reset
- `investment-created.php` - New investment notification
- `profit-credited.php` - Daily profit notification

## 🚀 Deployment

### Production Checklist
- [ ] Set `APP_ENV=production` in `.env`
- [ ] Set `APP_DEBUG=false` in `.env`
- [ ] Configure SSL certificate
- [ ] Set up database backups
- [ ] Configure email settings
- [ ] Set up monitoring
- [ ] Test all functionality
- [ ] Change default admin password
- [ ] Configure cron jobs
- [ ] Set up firewall rules

### Performance Optimization
1. **Enable OPcache**: Configure PHP OPcache for better performance
2. **Database Optimization**: Add indexes for frequently queried columns
3. **CDN**: Use a CDN for static assets
4. **Caching**: Implement Redis or Memcached for session storage
5. **Compression**: Enable Gzip compression

## 🐛 Troubleshooting

### Common Issues

**Database Connection Error**
- Check database credentials in `.env`
- Ensure MySQL service is running
- Verify database exists and user has permissions

**File Permission Errors**
- Set proper permissions on storage and uploads directories
- Ensure web server can write to these directories

**Email Not Sending**
- Check SMTP settings in `.env`
- Verify email credentials
- Check server firewall for SMTP ports

**404 Errors**
- Ensure mod_rewrite is enabled (Apache)
- Check Nginx configuration
- Verify `.htaccess` file exists

## 📞 Support

For technical support and questions:
- **Email**: <EMAIL>
- **Documentation**: Check the `/docs` directory for detailed documentation
- **Issues**: Report bugs and feature requests

## 📄 License

This project is licensed under the MIT License. See the `LICENSE` file for details.

## 🤝 Contributing

We welcome contributions! Please read our contributing guidelines before submitting pull requests.

---

**⚠️ Disclaimer**: This platform is for educational and demonstration purposes. Always comply with local laws and regulations when operating financial platforms. Cryptocurrency investments carry risks, and past performance does not guarantee future results.
