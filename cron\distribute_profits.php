<?php
/**
 * AstroGenix Daily Profit Distribution Cron Job
 * 
 * This script should be run daily to distribute profits to active investments
 * Add to crontab: 0 0 * * * /usr/bin/php /path/to/your/project/cron/distribute_profits.php
 */

// Set script execution time limit
set_time_limit(0);

// Define paths
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/src');

// Load environment variables
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Autoloader
spl_autoload_register(function ($class) {
    $prefix = 'AstroGenix\\';
    $baseDir = APP_PATH . '/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relativeClass = substr($class, $len);
    $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});

// Include helper functions
require_once APP_PATH . '/helpers.php';

use AstroGenix\Models\UserInvestment;
use AstroGenix\Models\User;
use AstroGenix\Models\Transaction;
use AstroGenix\Models\DailyProfit;
use AstroGenix\Core\Database;
use AstroGenix\Core\Logger;

try {
    $logger = new Logger();
    $logger->info('Starting daily profit distribution cron job');
    
    $investmentModel = new UserInvestment();
    $userModel = new User();
    $transactionModel = new Transaction();
    $dailyProfitModel = new DailyProfit();
    $db = Database::getInstance();
    
    // Get all investments that are due for profit distribution
    $investments = $investmentModel->getInvestmentsDueForProfit();
    
    $totalProcessed = 0;
    $totalProfitDistributed = 0;
    $errors = [];
    
    $logger->info('Found ' . count($investments) . ' investments due for profit distribution');
    
    foreach ($investments as $investment) {
        try {
            $db->beginTransaction();
            
            // Calculate daily profit
            $dailyProfit = $investment['daily_profit'];
            
            // Check if investment is still active and within duration
            if ($investment['status'] !== 'active') {
                $logger->warning("Investment {$investment['id']} is not active, skipping");
                $db->rollback();
                continue;
            }
            
            if (strtotime($investment['end_date']) <= time()) {
                $logger->info("Investment {$investment['id']} has reached end date, marking as completed");
                
                // Mark investment as completed
                $investmentModel->update($investment['id'], ['status' => 'completed']);
                
                $db->commit();
                continue;
            }
            
            // Add profit to user balance
            $userModel->updateBalance($investment['user_id'], $dailyProfit, 'add');
            $userModel->updateTotalEarned($investment['user_id'], $dailyProfit);
            
            // Create profit transaction
            $transactionModel->createProfitTransaction(
                $investment['user_id'],
                $dailyProfit,
                $investment['id']
            );
            
            // Record daily profit
            $dailyProfitModel->create([
                'investment_id' => $investment['id'],
                'user_id' => $investment['user_id'],
                'amount' => $dailyProfit,
                'profit_date' => date('Y-m-d')
            ]);
            
            // Update investment profit tracking
            $investmentModel->markProfitDistributed($investment['id'], $dailyProfit);
            
            $totalProcessed++;
            $totalProfitDistributed += $dailyProfit;
            
            $logger->info("Distributed profit of {$dailyProfit} USDT to user {$investment['user_id']} for investment {$investment['id']}");
            
            $db->commit();
            
        } catch (Exception $e) {
            $db->rollback();
            $error = "Error processing investment {$investment['id']}: " . $e->getMessage();
            $errors[] = $error;
            $logger->error($error);
        }
    }
    
    // Process referral commissions for new profits
    processReferralCommissions($totalProfitDistributed, $logger);
    
    $logger->info("Profit distribution completed. Processed: {$totalProcessed}, Total distributed: {$totalProfitDistributed} USDT");
    
    if (!empty($errors)) {
        $logger->warning('Errors occurred during profit distribution: ' . implode(', ', $errors));
    }
    
    // Send summary email to admin
    sendSummaryEmail($totalProcessed, $totalProfitDistributed, $errors);
    
    echo "Profit distribution completed successfully.\n";
    echo "Processed: {$totalProcessed} investments\n";
    echo "Total distributed: {$totalProfitDistributed} USDT\n";
    
} catch (Exception $e) {
    $error = 'Fatal error in profit distribution: ' . $e->getMessage();
    
    if (isset($logger)) {
        $logger->critical($error);
    } else {
        error_log($error);
    }
    
    echo "Error: {$error}\n";
    exit(1);
}

/**
 * Process referral commissions
 */
function processReferralCommissions($totalDistributed, $logger) {
    // This is a simplified version - in a real implementation,
    // you would calculate commissions based on individual investments
    $logger->info("Processing referral commissions for total distributed: {$totalDistributed} USDT");
    
    // Implementation would go here
    // For now, we'll just log that this step was reached
}

/**
 * Send summary email to admin
 */
function sendSummaryEmail($processed, $totalDistributed, $errors) {
    $subject = 'Daily Profit Distribution Summary - ' . date('Y-m-d');
    $message = "
    Daily Profit Distribution Summary
    Date: " . date('Y-m-d H:i:s') . "
    
    Investments Processed: {$processed}
    Total Profit Distributed: {$totalDistributed} USDT
    Errors: " . count($errors) . "
    
    ";
    
    if (!empty($errors)) {
        $message .= "Errors:\n" . implode("\n", $errors);
    }
    
    // Send email (simplified - in real implementation use proper mailer)
    $adminEmail = $_ENV['ADMIN_EMAIL'] ?? '<EMAIL>';
    mail($adminEmail, $subject, $message);
}

/**
 * Daily Profit Model (simplified)
 */
class DailyProfit extends \AstroGenix\Core\Model {
    protected $table = 'daily_profits';
    protected $fillable = ['investment_id', 'user_id', 'amount', 'profit_date'];
}
