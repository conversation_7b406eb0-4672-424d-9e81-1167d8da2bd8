<?php
/**
 * AstroGenix USDT Staking Platform
 * Entry Point
 */

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define constants
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/src');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('STORAGE_PATH', ROOT_PATH . '/storage');
define('PUBLIC_PATH', __DIR__);

// Load environment variables
if (file_exists(ROOT_PATH . '/.env')) {
    $lines = file(ROOT_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Autoloader
spl_autoload_register(function ($class) {
    $prefix = 'AstroGenix\\';
    $baseDir = APP_PATH . '/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relativeClass = substr($class, $len);
    $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});

// Helper functions
if (!function_exists('storage_path')) {
    function storage_path($path = '') {
        return STORAGE_PATH . ($path ? '/' . ltrim($path, '/') : '');
    }
}

if (!function_exists('public_path')) {
    function public_path($path = '') {
        return PUBLIC_PATH . ($path ? '/' . ltrim($path, '/') : '');
    }
}

if (!function_exists('config_path')) {
    function config_path($path = '') {
        return CONFIG_PATH . ($path ? '/' . ltrim($path, '/') : '');
    }
}

if (!function_exists('app_path')) {
    function app_path($path = '') {
        return APP_PATH . ($path ? '/' . ltrim($path, '/') : '');
    }
}

if (!function_exists('url')) {
    function url($path = '') {
        $baseUrl = $_ENV['APP_URL'] ?? 'http://localhost';
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }
}

if (!function_exists('asset')) {
    function asset($path) {
        return url('assets/' . ltrim($path, '/'));
    }
}

if (!function_exists('route')) {
    function route($name, $params = []) {
        // Simple route helper - in real implementation, use proper route resolver
        return url($name);
    }
}

if (!function_exists('csrf_token')) {
    function csrf_token() {
        session_start();
        if (!isset($_SESSION['_token'])) {
            $_SESSION['_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['_token'];
    }
}

if (!function_exists('old')) {
    function old($key, $default = '') {
        session_start();
        return $_SESSION['_old_input'][$key] ?? $default;
    }
}

if (!function_exists('trans')) {
    function trans($key, $params = [], $locale = null) {
        // Simple translation helper
        return $key; // In real implementation, load from language files
    }
}

// Create necessary directories
$directories = [
    storage_path('logs'),
    storage_path('cache'),
    storage_path('sessions'),
    storage_path('uploads'),
    public_path('uploads'),
    public_path('uploads/screenshots'),
    public_path('uploads/avatars'),
    public_path('uploads/news')
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Start application
try {
    $router = new AstroGenix\Core\Router();
    
    // Define routes
    
    // Home routes
    $router->get('/', 'HomeController@index');
    $router->get('/about', 'HomeController@about');
    $router->get('/packages', 'HomeController@packages');
    $router->get('/packages/{id}', 'HomeController@packageDetails');
    $router->get('/news', 'HomeController@news');
    $router->get('/news/{slug}', 'HomeController@newsArticle');
    $router->get('/faq', 'HomeController@faq');
    $router->get('/contact', 'HomeController@contact');
    $router->post('/contact', 'HomeController@contact');
    $router->get('/calculator', 'HomeController@calculator');
    $router->post('/calculator', 'HomeController@calculator');
    $router->get('/lang/{lang}', 'HomeController@setLanguage');
    
    // Auth routes
    $router->get('/login', 'AuthController@login');
    $router->post('/login', 'AuthController@login');
    $router->get('/register', 'AuthController@register');
    $router->post('/register', 'AuthController@register');
    $router->get('/logout', 'AuthController@logout');
    $router->get('/forgot-password', 'AuthController@forgotPassword');
    $router->post('/forgot-password', 'AuthController@forgotPassword');
    $router->get('/reset-password/{token}', 'AuthController@resetPassword');
    $router->post('/reset-password/{token}', 'AuthController@resetPassword');
    $router->get('/verify-email/{user}/{token}', 'AuthController@verifyEmail');
    
    // User dashboard routes
    $router->get('/dashboard', 'DashboardController@index');
    $router->get('/dashboard/profile', 'DashboardController@profile');
    $router->post('/dashboard/profile', 'DashboardController@updateProfile');
    $router->get('/dashboard/investments', 'DashboardController@investments');
    $router->get('/dashboard/invest', 'DashboardController@invest');
    $router->post('/dashboard/invest', 'DashboardController@createInvestment');
    $router->get('/dashboard/transactions', 'DashboardController@transactions');
    $router->get('/dashboard/deposit', 'DashboardController@deposit');
    $router->post('/dashboard/deposit', 'DashboardController@createDeposit');
    $router->get('/dashboard/withdraw', 'DashboardController@withdraw');
    $router->post('/dashboard/withdraw', 'DashboardController@createWithdrawal');
    $router->get('/dashboard/referrals', 'DashboardController@referrals');
    $router->get('/dashboard/tasks', 'DashboardController@tasks');
    $router->post('/dashboard/tasks/{id}/claim', 'DashboardController@claimTask');
    $router->get('/dashboard/support', 'DashboardController@support');
    $router->post('/dashboard/support', 'DashboardController@createTicket');
    $router->get('/dashboard/support/{id}', 'DashboardController@viewTicket');
    $router->post('/dashboard/support/{id}/reply', 'DashboardController@replyTicket');
    $router->get('/dashboard/security', 'DashboardController@security');
    $router->post('/dashboard/security/password', 'DashboardController@changePassword');
    $router->post('/dashboard/security/2fa/enable', 'DashboardController@enableTwoFactor');
    $router->post('/dashboard/security/2fa/disable', 'DashboardController@disableTwoFactor');
    
    // Admin routes
    $router->get('/admin', 'AdminController@dashboard');
    $router->get('/admin/login', 'AdminController@login');
    $router->post('/admin/login', 'AdminController@login');
    $router->get('/admin/logout', 'AdminController@logout');
    $router->get('/admin/users', 'AdminController@users');
    $router->get('/admin/users/{id}', 'AdminController@userDetails');
    $router->post('/admin/users/{id}/update', 'AdminController@updateUser');
    $router->get('/admin/investments', 'AdminController@investments');
    $router->get('/admin/packages', 'AdminController@packages');
    $router->post('/admin/packages', 'AdminController@createPackage');
    $router->post('/admin/packages/{id}/update', 'AdminController@updatePackage');
    $router->post('/admin/packages/{id}/delete', 'AdminController@deletePackage');
    $router->get('/admin/transactions', 'AdminController@transactions');
    $router->post('/admin/transactions/{id}/approve', 'AdminController@approveTransaction');
    $router->post('/admin/transactions/{id}/reject', 'AdminController@rejectTransaction');
    $router->get('/admin/news', 'AdminController@news');
    $router->get('/admin/news/create', 'AdminController@createNews');
    $router->post('/admin/news/create', 'AdminController@storeNews');
    $router->get('/admin/news/{id}/edit', 'AdminController@editNews');
    $router->post('/admin/news/{id}/update', 'AdminController@updateNews');
    $router->post('/admin/news/{id}/delete', 'AdminController@deleteNews');
    $router->get('/admin/support', 'AdminController@support');
    $router->get('/admin/support/{id}', 'AdminController@viewSupportTicket');
    $router->post('/admin/support/{id}/reply', 'AdminController@replySupportTicket');
    $router->get('/admin/settings', 'AdminController@settings');
    $router->post('/admin/settings', 'AdminController@updateSettings');
    
    // API routes
    $router->get('/api/stats', 'ApiController@stats');
    $router->get('/api/packages', 'ApiController@packages');
    $router->post('/api/calculate', 'ApiController@calculate');
    
    // Handle the request
    $router->dispatch();
    
} catch (Exception $e) {
    // Error handling
    error_log('Application Error: ' . $e->getMessage());
    
    if ($_ENV['APP_DEBUG'] ?? false) {
        echo '<h1>Application Error</h1>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        http_response_code(500);
        echo '<h1>Internal Server Error</h1>';
        echo '<p>Something went wrong. Please try again later.</p>';
    }
}
