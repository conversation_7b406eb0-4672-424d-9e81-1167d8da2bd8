{"name": "astrogenix/usdt-staking-platform", "description": "Professional USDT Staking Platform - Secure and profitable cryptocurrency investment platform", "type": "project", "keywords": ["usdt", "staking", "cryptocurrency", "investment", "defi", "passive-income", "blockchain"], "license": "MIT", "authors": [{"name": "AstroGenix Team", "email": "<EMAIL>", "homepage": "https://astrogenix.com"}], "require": {"php": ">=7.4", "ext-pdo": "*", "ext-mysqli": "*", "ext-openssl": "*", "ext-curl": "*", "ext-gd": "*", "ext-mbstring": "*", "ext-json": "*", "ext-fileinfo": "*"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.6", "phpstan/phpstan": "^1.0"}, "autoload": {"psr-4": {"AstroGenix\\": "src/"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"AstroGenix\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "cs-check": "phpcs --standard=PSR12 src/", "cs-fix": "phpcbf --standard=PSR12 src/", "analyse": "phpstan analyse src/ --level=5", "post-install-cmd": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-update-cmd": ["@php artisan optimize"], "setup": ["@composer install", "@php -r \"file_exists('.env') || copy('.env.example', '.env');\"", "@php -r \"echo 'Please configure your .env file and run database migrations.\\n';\""]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true}}, "minimum-stability": "stable", "prefer-stable": true, "extra": {"platform": {"php": "7.4"}}, "support": {"email": "<EMAIL>", "issues": "https://github.com/astrogenix/platform/issues", "source": "https://github.com/astrogenix/platform"}}