<?php
/**
 * Database Check Script for AstroGenix
 * This script checks if all required tables exist in the database
 */

header('Content-Type: text/html; charset=utf-8');

// Load configuration
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $lines = explode("\n", $envContent);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"');
        }
    }
}

// Database configuration
$dbConfig = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'port' => $_ENV['DB_PORT'] ?? '3306',
    'database' => $_ENV['DB_DATABASE'] ?? 'astrogenix',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? ''
];

// Required tables
$requiredTables = [
    'users',
    'investment_packages',
    'user_investments',
    'daily_profits',
    'transactions',
    'referral_commissions',
    'tasks',
    'user_tasks',
    'news_categories',
    'news',
    'support_tickets',
    'support_messages',
    'system_settings',
    'admin_users'
];

?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroGenix - Проверка базы данных</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .table-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .table-item.exists { background: #d4edda; color: #155724; }
        .table-item.missing { background: #f8d7da; color: #721c24; }
        .status { font-weight: bold; }
        .summary { margin-top: 30px; padding: 20px; border-radius: 5px; text-align: center; }
        .summary.complete { background: #d4edda; color: #155724; }
        .summary.incomplete { background: #f8d7da; color: #721c24; }
        .info { background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .actions { text-align: center; margin-top: 20px; }
        .btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; font-weight: bold; }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AstroGenix</h1>
            <h2>Проверка базы данных</h2>
        </div>

        <?php
        try {
            // Connect to database
            $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
            $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo '<div class="info">✅ Подключение к базе данных успешно</div>';
            
            // Get existing tables
            $stmt = $pdo->query("SHOW TABLES");
            $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $missingTables = [];
            $totalTables = count($requiredTables);
            $existingCount = 0;
            
            echo '<h3>Проверка таблиц:</h3>';
            
            foreach ($requiredTables as $table) {
                $exists = in_array($table, $existingTables);
                if ($exists) {
                    $existingCount++;
                    echo '<div class="table-item exists">';
                    echo '<span>' . $table . '</span>';
                    echo '<span class="status">✅ Существует</span>';
                    echo '</div>';
                } else {
                    $missingTables[] = $table;
                    echo '<div class="table-item missing">';
                    echo '<span>' . $table . '</span>';
                    echo '<span class="status">❌ Отсутствует</span>';
                    echo '</div>';
                }
            }
            
            // Show summary
            if (empty($missingTables)) {
                echo '<div class="summary complete">';
                echo '<h3>✅ База данных настроена правильно</h3>';
                echo '<p>Все ' . $totalTables . ' таблиц найдены в базе данных.</p>';
                echo '</div>';
                
                echo '<div class="actions">';
                echo '<a href="index.php" class="btn btn-success">Перейти к платформе</a>';
                echo '</div>';
            } else {
                echo '<div class="summary incomplete">';
                echo '<h3>❌ База данных настроена неправильно</h3>';
                echo '<p>Найдено ' . $existingCount . ' из ' . $totalTables . ' таблиц.</p>';
                echo '<p>Отсутствующие таблицы: ' . implode(', ', $missingTables) . '</p>';
                echo '</div>';
                
                echo '<div class="actions">';
                echo '<a href="reset_database.php?confirm=yes_delete_all_data" class="btn btn-danger">Сбросить базу данных</a>';
                echo '<a href="install.php" class="btn btn-primary">Переустановить</a>';
                echo '</div>';
            }
            
            // Show additional info
            echo '<div class="info">';
            echo '<h4>Дополнительная информация:</h4>';
            echo '<p><strong>База данных:</strong> ' . $dbConfig['database'] . '</p>';
            echo '<p><strong>Хост:</strong> ' . $dbConfig['host'] . ':' . $dbConfig['port'] . '</p>';
            echo '<p><strong>Всего таблиц в БД:</strong> ' . count($existingTables) . '</p>';
            
            if (count($existingTables) > $totalTables) {
                $extraTables = array_diff($existingTables, $requiredTables);
                echo '<p><strong>Дополнительные таблицы:</strong> ' . implode(', ', $extraTables) . '</p>';
            }
            echo '</div>';
            
        } catch (PDOException $e) {
            echo '<div class="summary incomplete">';
            echo '<h3>❌ Ошибка подключения к базе данных</h3>';
            echo '<p>Не удалось подключиться к базе данных: ' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '</div>';
            
            echo '<div class="actions">';
            echo '<a href="check_system.php" class="btn btn-primary">Проверить систему</a>';
            echo '<a href="install.php" class="btn btn-primary">Установить заново</a>';
            echo '</div>';
        }
        ?>
    </div>
</body>
</html>
