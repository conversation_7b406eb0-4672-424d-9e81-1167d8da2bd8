<?php

namespace AstroGenix\Core;

use Exception;

/**
 * Base Controller Class
 */
abstract class Controller
{
    protected $request;
    protected $response;
    protected $session;
    protected $user;
    
    public function __construct()
    {
        $this->request = new Request();
        $this->response = new Response();
        $this->session = new Session();
        
        // Load current user if authenticated
        $this->loadCurrentUser();
    }
    
    protected function loadCurrentUser(): void
    {
        $userId = $this->session->get('user_id');
        if ($userId) {
            $userModel = new \AstroGenix\Models\User();
            $this->user = $userModel->find($userId);
        }
    }
    
    protected function requireAuth(): void
    {
        if (!$this->user) {
            $this->redirect('/login');
        }
    }
    
    protected function requireGuest(): void
    {
        if ($this->user) {
            $this->redirect('/dashboard');
        }
    }
    
    protected function requireAdmin(): void
    {
        $adminId = $this->session->get('admin_id');
        if (!$adminId) {
            $this->redirect('/admin/login');
        }
    }
    
    protected function json(array $data, int $statusCode = 200): void
    {
        $this->response->json($data, $statusCode);
    }
    
    protected function success(string $message, array $data = []): void
    {
        $this->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }
    
    protected function error(string $message, int $statusCode = 400, array $errors = []): void
    {
        $this->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $statusCode);
    }
    
    protected function redirect(string $url): void
    {
        $this->response->redirect($url);
    }
    
    protected function view(string $template, array $data = []): void
    {
        $view = new View();
        $view->render($template, array_merge($data, [
            'user' => $this->user,
            'session' => $this->session,
            'request' => $this->request
        ]));
    }
    
    protected function validate(array $data, array $rules): array
    {
        $validator = new Validator();
        return $validator->validate($data, $rules);
    }
    
    protected function uploadFile(string $fieldName, string $uploadPath = 'uploads/'): ?string
    {
        if (!isset($_FILES[$fieldName]) || $_FILES[$fieldName]['error'] !== UPLOAD_ERR_OK) {
            return null;
        }
        
        $file = $_FILES[$fieldName];
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
        $maxSize = 5 * 1024 * 1024; // 5MB
        
        // Validate file type
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            throw new Exception('Invalid file type. Allowed types: ' . implode(', ', $allowedTypes));
        }
        
        // Validate file size
        if ($file['size'] > $maxSize) {
            throw new Exception('File size too large. Maximum size: 5MB');
        }
        
        // Generate unique filename
        $filename = uniqid() . '_' . time() . '.' . $extension;
        $fullPath = rtrim($uploadPath, '/') . '/' . $filename;
        
        // Create directory if it doesn't exist
        $directory = dirname($fullPath);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $fullPath)) {
            return $filename;
        }
        
        throw new Exception('Failed to upload file');
    }
    
    protected function deleteFile(string $filePath): bool
    {
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        return false;
    }
    
    protected function paginate(array $data, int $page, int $perPage): array
    {
        $total = count($data);
        $totalPages = ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;
        
        return [
            'data' => array_slice($data, $offset, $perPage),
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => $totalPages,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1,
            ]
        ];
    }
    
    protected function sanitizeInput(string $input): string
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    protected function generateCsrfToken(): string
    {
        $token = bin2hex(random_bytes(32));
        $this->session->set('csrf_token', $token);
        return $token;
    }
    
    protected function validateCsrfToken(string $token): bool
    {
        $sessionToken = $this->session->get('csrf_token');
        return $sessionToken && hash_equals($sessionToken, $token);
    }
    
    protected function log(string $message, string $level = 'info', array $context = []): void
    {
        $logger = new Logger();
        $logger->log($level, $message, $context);
    }
    
    protected function cache(string $key, $value = null, int $ttl = 3600)
    {
        $cache = new Cache();
        
        if ($value === null) {
            return $cache->get($key);
        }
        
        return $cache->set($key, $value, $ttl);
    }
    
    protected function sendEmail(string $to, string $subject, string $template, array $data = []): bool
    {
        $mailer = new Mailer();
        return $mailer->send($to, $subject, $template, $data);
    }
    
    protected function getCurrentLanguage(): string
    {
        return $this->session->get('language', 'en');
    }
    
    protected function setLanguage(string $language): void
    {
        $supportedLanguages = ['en', 'ru'];
        if (in_array($language, $supportedLanguages)) {
            $this->session->set('language', $language);
        }
    }
    
    protected function translate(string $key, array $params = []): string
    {
        $translator = new Translator();
        return $translator->translate($key, $params, $this->getCurrentLanguage());
    }
    
    protected function formatCurrency(float $amount, string $currency = 'USDT'): string
    {
        return number_format($amount, 6) . ' ' . $currency;
    }
    
    protected function formatDate(string $date, string $format = 'Y-m-d H:i:s'): string
    {
        return date($format, strtotime($date));
    }
    
    protected function getClientIp(): string
    {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    protected function rateLimit(string $key, int $maxAttempts = 60, int $timeWindow = 60): bool
    {
        $cache = new Cache();
        $attempts = $cache->get($key) ?? 0;
        
        if ($attempts >= $maxAttempts) {
            return false;
        }
        
        $cache->set($key, $attempts + 1, $timeWindow);
        return true;
    }
}
