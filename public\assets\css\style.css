/* AstroGenix - Modern USDT Staking Platform Styles */

:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #374151;
    background-color: #ffffff;
    overflow-x: hidden;
}

.main-content {
    padding-top: 76px; /* Account for fixed navbar */
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-spinner.show {
    opacity: 1;
    visibility: visible;
}

/* Navigation */
.navbar {
    backdrop-filter: blur(10px);
    background: rgba(31, 41, 55, 0.95) !important;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--dark-color);
    color: white;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(99, 102, 241, 0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #d1d5db;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-stats {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #d1d5db;
    margin: 0;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    z-index: 2;
}

.crypto-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.crypto-card .card-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.crypto-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.crypto-card .card-body {
    padding: 1.5rem;
}

.investment-preview {
    color: white;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.preview-item .label {
    color: #d1d5db;
}

.preview-item .value {
    font-weight: 600;
}

.progress {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.progress-bar {
    background: var(--gradient-secondary);
    border-radius: 4px;
}

/* Buttons */
.btn {
    font-weight: 600;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--gradient-primary);
    color: white;
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-2px);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Sections */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 2rem;
}

/* Features Section */
.features-section {
    padding: 5rem 0;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 1rem;
    background: white;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid #f3f4f6;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.feature-card h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.feature-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Plans Section */
.plans-section {
    padding: 5rem 0;
    background: #f8fafc;
}

.plan-card {
    background: white;
    border-radius: 1.5rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    height: 100%;
    border: 1px solid #f3f4f6;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.plan-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.plan-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    z-index: 1;
}

.plan-header {
    padding: 2rem 2rem 1rem;
    text-align: center;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.return-rate {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.return-period {
    font-size: 1rem;
    color: #6b7280;
}

.plan-body {
    padding: 2rem;
}

.plan-features {
    margin-bottom: 1.5rem;
}

.feature {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: #374151;
}

.feature i {
    color: var(--primary-color);
    margin-right: 0.75rem;
    width: 20px;
}

.plan-description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.6;
}

.plan-footer {
    padding: 0 2rem 2rem;
}

/* Activity Section */
.activity-section {
    padding: 5rem 0;
}

.activity-feed {
    background: white;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    padding: 1.5rem;
    border: 1px solid #f3f4f6;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: #f0fdf4;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    color: #374151;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.875rem;
    color: #6b7280;
}

.top-investors {
    background: white;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    padding: 1.5rem;
    border: 1px solid #f3f4f6;
}

.investor-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.investor-item:last-child {
    border-bottom: none;
}

.investor-rank {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
}

.investor-info {
    flex: 1;
}

.investor-name {
    font-weight: 600;
    color: #374151;
}

.investor-amount {
    font-size: 0.875rem;
    color: #6b7280;
}

.investor-badge {
    font-size: 1.25rem;
}

/* News Section */
.news-section {
    padding: 5rem 0;
}

.news-card {
    background: white;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid #f3f4f6;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.news-image {
    height: 200px;
    overflow: hidden;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-content {
    padding: 1.5rem;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.news-date {
    color: #6b7280;
}

.news-category {
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.news-title {
    margin-bottom: 1rem;
}

.news-title a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.news-title a:hover {
    color: var(--primary-color);
}

.news-excerpt {
    color: #6b7280;
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    padding: 5rem 0;
    background: var(--gradient-primary);
    color: white;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: var(--dark-color) !important;
    color: #d1d5db;
}

.footer h5,
.footer h6 {
    color: white;
    font-weight: 600;
}

.footer a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--primary-color);
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

/* Back to Top */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: none;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .plan-card.featured {
        transform: none;
    }
    
    .cta-title {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .feature-card,
    .plan-card {
        margin-bottom: 2rem;
    }
}
