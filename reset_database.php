<?php
/**
 * Database Reset Script for AstroGenix
 * This script will drop all tables and recreate them
 * Use with caution - this will delete all data!
 */

// Check if running from command line or web
$isCommandLine = php_sapi_name() === 'cli';

if (!$isCommandLine) {
    // Basic security check for web access
    if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes_delete_all_data') {
        die('Access denied. Use command line or add ?confirm=yes_delete_all_data to URL');
    }
}

// Load configuration
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $lines = explode("\n", $envContent);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Database configuration
$dbConfig = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'port' => $_ENV['DB_PORT'] ?? '3306',
    'database' => $_ENV['DB_DATABASE'] ?? 'astrogenix',
    'username' => $_ENV['DB_USERNAME'] ?? 'root',
    'password' => $_ENV['DB_PASSWORD'] ?? ''
];

try {
    // Connect to database
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // Disable foreign key checks
    $pdo->exec('SET FOREIGN_KEY_CHECKS = 0');
    echo "Disabled foreign key checks.\n";
    
    // Get all tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Drop all tables
    foreach ($tables as $table) {
        $pdo->exec("DROP TABLE IF EXISTS `$table`");
        echo "Dropped table: $table\n";
    }
    
    // Re-enable foreign key checks
    $pdo->exec('SET FOREIGN_KEY_CHECKS = 1');
    echo "Re-enabled foreign key checks.\n";
    
    // Recreate tables from schema
    echo "Recreating tables from schema...\n";

    $schema = file_get_contents('database/schema.sql');

    // Remove comments and empty lines, then split by semicolon
    $lines = explode("\n", $schema);
    $cleanedSql = '';

    foreach ($lines as $line) {
        $line = trim($line);
        // Skip comments and empty lines
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }
        $cleanedSql .= $line . "\n";
    }

    // Split SQL into individual statements
    $statements = explode(';', $cleanedSql);

    // Separate table creation from foreign key constraints
    $createStatements = [];
    $alterStatements = [];
    $otherStatements = [];

    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement)) continue;

        if (stripos($statement, 'CREATE TABLE') !== false) {
            $createStatements[] = $statement;
        } elseif (stripos($statement, 'ALTER TABLE') !== false && stripos($statement, 'FOREIGN KEY') !== false) {
            $alterStatements[] = $statement;
        } elseif (stripos($statement, 'CREATE DATABASE') === false && stripos($statement, 'USE ') === false) {
            $otherStatements[] = $statement;
        }
    }

    // Execute in order: other statements, create tables, then foreign keys
    echo "Creating tables...\n";
    foreach ($createStatements as $statement) {
        try {
            $pdo->exec($statement);
            preg_match('/CREATE TABLE `?(\w+)`?/', $statement, $matches);
            if (isset($matches[1])) {
                echo "✓ Created table: {$matches[1]}\n";
            }
        } catch (PDOException $e) {
            echo "✗ Error creating table: " . $e->getMessage() . "\n";
        }
    }

    echo "\nExecuting other statements...\n";
    foreach ($otherStatements as $statement) {
        try {
            $pdo->exec($statement);
            echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
        } catch (PDOException $e) {
            echo "✗ Error: " . $e->getMessage() . "\n";
        }
    }

    echo "\nAdding foreign key constraints...\n";
    foreach ($alterStatements as $statement) {
        try {
            $pdo->exec($statement);
            echo "✓ Added constraint\n";
        } catch (PDOException $e) {
            echo "✗ Error adding constraint: " . $e->getMessage() . "\n";
        }
    }
    
    echo "Tables recreated successfully.\n";
    
    // Insert initial data
    echo "Inserting initial data...\n";

    $seedData = file_get_contents('database/seed_data.sql');

    // Remove comments and empty lines, then split by semicolon
    $lines = explode("\n", $seedData);
    $cleanedSql = '';

    foreach ($lines as $line) {
        $line = trim($line);
        // Skip comments and empty lines
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }
        $cleanedSql .= $line . "\n";
    }

    // Split SQL into individual statements
    $statements = explode(';', $cleanedSql);

    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                if (strpos($statement, 'INSERT INTO') !== false) {
                    preg_match('/INSERT INTO `?(\w+)`?/', $statement, $matches);
                    if (isset($matches[1])) {
                        echo "Inserted data into: {$matches[1]}\n";
                    }
                }
            } catch (PDOException $e) {
                echo "Warning: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "Initial data inserted successfully.\n";
    echo "\nDatabase reset completed successfully!\n";
    echo "You can now run the installation wizard again.\n";
    
    // Remove installation marker if exists
    if (file_exists('INSTALLED')) {
        unlink('INSTALLED');
        echo "Removed installation marker.\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
