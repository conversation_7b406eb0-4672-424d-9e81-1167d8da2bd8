# AstroGenix - Руководство по устранению неполадок

## Ошибка установки: "Duplicate column name 'referral_code'"

### Описание проблемы
При установке системы появляется ошибка:
```
Installation failed. SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'referral_code'
```

### Причина
Эта ошибка возникает когда:
1. База данных уже содержит таблицы с предыдущей установки
2. Произошла неполная установка, которая оставила частично созданные таблицы
3. В схеме базы данных есть дублирующиеся поля или индексы

### Решение

#### Вариант 1: Использование скрипта сброса (Рекомендуется)

1. Запустите скрипт сброса базы данных:
```bash
php reset_database.php
```

Или через веб-браузер:
```
http://ваш-домен.com/reset_database.php?confirm=yes_delete_all_data
```

2. После успешного сброса запустите установку заново:
```
http://ваш-домен.com/install.php
```

#### Вариант 2: Ручной сброс через MySQL

1. Подключитесь к MySQL:
```bash
mysql -u root -p
```

2. Удалите базу данных:
```sql
DROP DATABASE IF EXISTS astrogenix;
```

3. Создайте базу данных заново:
```sql
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

4. Запустите установку заново.

#### Вариант 3: Очистка таблиц

Если вы хотите сохранить базу данных, но очистить таблицы:

```sql
USE astrogenix;

SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS investment_packages;
DROP TABLE IF EXISTS user_investments;
DROP TABLE IF EXISTS daily_profits;
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS referral_commissions;
DROP TABLE IF EXISTS tasks;
DROP TABLE IF EXISTS user_tasks;
DROP TABLE IF EXISTS news_categories;
DROP TABLE IF EXISTS news;
DROP TABLE IF EXISTS support_tickets;
DROP TABLE IF EXISTS support_messages;
DROP TABLE IF EXISTS system_settings;
DROP TABLE IF EXISTS admin_users;

SET FOREIGN_KEY_CHECKS = 1;
```

### Проверка успешности установки

После установки проверьте:

1. Файл `INSTALLED` должен быть создан в корневой директории
2. Файл `.env` должен содержать правильные настройки базы данных
3. Все таблицы должны быть созданы:

```sql
USE astrogenix;
SHOW TABLES;
```

Должно показать 14 таблиц:
- admin_users
- daily_profits
- investment_packages
- news
- news_categories
- referral_commissions
- support_messages
- support_tickets
- system_settings
- tasks
- transactions
- user_investments
- user_tasks
- users

### Дополнительные проблемы

#### Ошибка доступа к базе данных
```
Database access denied. Please check your database credentials.
```

**Решение:**
1. Проверьте правильность данных для подключения к БД
2. Убедитесь, что пользователь БД имеет все необходимые права
3. Проверьте, что MySQL сервер запущен

#### Ошибка "Database does not exist"
```
Database does not exist. Please create the database first.
```

**Решение:**
Создайте базу данных вручную:
```sql
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### Проблемы с правами доступа к файлам

Убедитесь, что веб-сервер имеет права на запись в следующие директории:
```bash
chmod 755 storage/
chmod 755 storage/logs/
chmod 755 storage/cache/
chmod 755 storage/sessions/
chmod 755 public/uploads/
```

### Логи ошибок

Проверьте логи для получения дополнительной информации:
- Логи PHP: `/var/log/apache2/error.log` или `/var/log/nginx/error.log`
- Логи MySQL: `/var/log/mysql/error.log`
- Логи приложения: `storage/logs/`

### Контакты поддержки

Если проблема не решается:
1. Проверьте все шаги в данном руководстве
2. Убедитесь, что все требования системы выполнены
3. Обратитесь к разработчику с подробным описанием ошибки

### Требования системы

Убедитесь, что ваша система соответствует требованиям:
- PHP 7.4 или выше
- MySQL 5.7 или выше / MariaDB 10.2 или выше
- Apache 2.4 или Nginx 1.18
- Модули PHP: PDO, PDO_MySQL, mbstring, openssl, json, curl
- Минимум 512MB RAM
- Минимум 1GB свободного места на диске
