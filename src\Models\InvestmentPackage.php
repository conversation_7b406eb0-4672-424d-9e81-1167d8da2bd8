<?php

namespace AstroGenix\Models;

use AstroGenix\Core\Model;

/**
 * Investment Package Model
 */
class InvestmentPackage extends Model
{
    protected $table = 'investment_packages';
    protected $fillable = [
        'name', 'description', 'min_amount', 'max_amount',
        'daily_percentage', 'duration_days', 'total_return_percentage',
        'status', 'sort_order'
    ];
    
    public function getActivePackages(): array
    {
        return $this->findAll(['status' => 'active'], 'sort_order ASC');
    }
    
    public function getPackageForAmount(float $amount): ?array
    {
        $sql = "
            SELECT * FROM {$this->table} 
            WHERE status = 'active' 
            AND min_amount <= :amount 
            AND max_amount >= :amount 
            ORDER BY sort_order ASC 
            LIMIT 1
        ";
        
        return $this->db->fetch($sql, ['amount' => $amount]);
    }
    
    public function calculateProfit(int $packageId, float $amount): array
    {
        $package = $this->find($packageId);
        if (!$package) {
            throw new \Exception('Investment package not found');
        }
        
        if ($amount < $package['min_amount'] || $amount > $package['max_amount']) {
            throw new \Exception('Investment amount is outside package limits');
        }
        
        $dailyProfit = ($amount * $package['daily_percentage']) / 100;
        $totalProfit = ($amount * $package['total_return_percentage']) / 100;
        $endDate = date('Y-m-d H:i:s', strtotime("+{$package['duration_days']} days"));
        
        return [
            'daily_profit' => $dailyProfit,
            'total_profit' => $totalProfit,
            'duration_days' => $package['duration_days'],
            'end_date' => $endDate,
            'daily_percentage' => $package['daily_percentage'],
            'total_return_percentage' => $package['total_return_percentage']
        ];
    }
    
    public function getPackageStatistics(int $packageId): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_investments,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount,
                SUM(total_profit_earned) as total_profits_paid,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_investments,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_investments
            FROM user_investments 
            WHERE package_id = :package_id
        ";
        
        return $this->db->fetch($sql, ['package_id' => $packageId]) ?: [];
    }
    
    public function getAllPackageStatistics(): array
    {
        $packages = $this->getActivePackages();
        $statistics = [];
        
        foreach ($packages as $package) {
            $stats = $this->getPackageStatistics($package['id']);
            $statistics[] = array_merge($package, $stats);
        }
        
        return $statistics;
    }
    
    public function updatePackage(int $id, array $data): bool
    {
        // Validate percentage values
        if (isset($data['daily_percentage']) && ($data['daily_percentage'] < 0 || $data['daily_percentage'] > 10)) {
            throw new \Exception('Daily percentage must be between 0 and 10');
        }
        
        if (isset($data['total_return_percentage']) && ($data['total_return_percentage'] < 0 || $data['total_return_percentage'] > 500)) {
            throw new \Exception('Total return percentage must be between 0 and 500');
        }
        
        // Validate amounts
        if (isset($data['min_amount']) && $data['min_amount'] < 0) {
            throw new \Exception('Minimum amount cannot be negative');
        }
        
        if (isset($data['max_amount']) && isset($data['min_amount']) && $data['max_amount'] < $data['min_amount']) {
            throw new \Exception('Maximum amount cannot be less than minimum amount');
        }
        
        return $this->update($id, $data);
    }
    
    public function createPackage(array $data): int
    {
        // Validate required fields
        $required = ['name', 'min_amount', 'max_amount', 'daily_percentage', 'duration_days'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new \Exception("Field {$field} is required");
            }
        }
        
        // Calculate total return percentage if not provided
        if (!isset($data['total_return_percentage'])) {
            $data['total_return_percentage'] = $data['daily_percentage'] * $data['duration_days'];
        }
        
        // Set default sort order if not provided
        if (!isset($data['sort_order'])) {
            $maxOrder = $this->db->fetch("SELECT MAX(sort_order) as max_order FROM {$this->table}");
            $data['sort_order'] = ($maxOrder['max_order'] ?? 0) + 1;
        }
        
        return $this->create($data);
    }
    
    public function reorderPackages(array $packageIds): bool
    {
        $this->db->beginTransaction();
        
        try {
            foreach ($packageIds as $index => $packageId) {
                $this->update($packageId, ['sort_order' => $index + 1]);
            }
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    public function toggleStatus(int $id): bool
    {
        $package = $this->find($id);
        if (!$package) {
            return false;
        }
        
        $newStatus = $package['status'] === 'active' ? 'inactive' : 'active';
        return $this->update($id, ['status' => $newStatus]);
    }
    
    public function getPopularPackages(int $limit = 5): array
    {
        $sql = "
            SELECT 
                p.*,
                COUNT(ui.id) as investment_count,
                SUM(ui.amount) as total_invested
            FROM {$this->table} p
            LEFT JOIN user_investments ui ON p.id = ui.package_id
            WHERE p.status = 'active'
            GROUP BY p.id
            ORDER BY investment_count DESC, total_invested DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, ['limit' => $limit]);
    }
    
    public function validateInvestmentAmount(int $packageId, float $amount): bool
    {
        $package = $this->find($packageId);
        if (!$package || $package['status'] !== 'active') {
            return false;
        }
        
        return $amount >= $package['min_amount'] && $amount <= $package['max_amount'];
    }
    
    public function getPackagePerformance(): array
    {
        $sql = "
            SELECT 
                p.name,
                p.daily_percentage,
                p.duration_days,
                COUNT(ui.id) as total_investments,
                SUM(ui.amount) as total_amount,
                AVG(ui.amount) as avg_investment,
                SUM(ui.total_profit_earned) as total_profits_paid,
                (SUM(ui.total_profit_earned) / SUM(ui.amount)) * 100 as roi_percentage
            FROM {$this->table} p
            LEFT JOIN user_investments ui ON p.id = ui.package_id
            WHERE p.status = 'active'
            GROUP BY p.id
            ORDER BY total_amount DESC
        ";
        
        return $this->db->fetchAll($sql);
    }
}
