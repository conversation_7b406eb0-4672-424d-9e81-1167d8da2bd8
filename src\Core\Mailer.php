<?php

namespace AstroGenix\Core;

/**
 * Simple Email Mailer Class
 */
class Mailer
{
    private $config;
    
    public function __construct()
    {
        $this->config = require config_path('app.php');
    }
    
    public function send(string $to, string $subject, string $template, array $data = []): bool
    {
        try {
            // Load email template
            $body = $this->loadTemplate($template, $data);
            
            // Prepare headers
            $headers = $this->prepareHeaders();
            
            // Send email
            return mail($to, $subject, $body, $headers);
            
        } catch (\Exception $e) {
            error_log('Email sending failed: ' . $e->getMessage());
            return false;
        }
    }
    
    public function sendRaw(string $to, string $subject, string $body, array $headers = []): bool
    {
        try {
            $defaultHeaders = $this->prepareHeaders();
            $allHeaders = array_merge($defaultHeaders, $headers);
            
            return mail($to, $subject, $body, implode("\r\n", $allHeaders));
            
        } catch (\Exception $e) {
            error_log('Email sending failed: ' . $e->getMessage());
            return false;
        }
    }
    
    public function sendHtml(string $to, string $subject, string $htmlBody): bool
    {
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . $this->config['mail']['from']['name'] . ' <' . $this->config['mail']['from']['address'] . '>',
            'Reply-To: ' . $this->config['mail']['from']['address'],
            'X-Mailer: AstroGenix Mailer'
        ];
        
        return mail($to, $subject, $htmlBody, implode("\r\n", $headers));
    }
    
    public function queue(string $to, string $subject, string $template, array $data = []): bool
    {
        // Simple queue implementation - store in database or file
        $queueData = [
            'to' => $to,
            'subject' => $subject,
            'template' => $template,
            'data' => $data,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $queueFile = storage_path('mail_queue.json');
        $queue = [];
        
        if (file_exists($queueFile)) {
            $queue = json_decode(file_get_contents($queueFile), true) ?: [];
        }
        
        $queue[] = $queueData;
        
        return file_put_contents($queueFile, json_encode($queue, JSON_PRETTY_PRINT)) !== false;
    }
    
    public function processQueue(): int
    {
        $queueFile = storage_path('mail_queue.json');
        
        if (!file_exists($queueFile)) {
            return 0;
        }
        
        $queue = json_decode(file_get_contents($queueFile), true) ?: [];
        $processed = 0;
        $remaining = [];
        
        foreach ($queue as $item) {
            if ($this->send($item['to'], $item['subject'], $item['template'], $item['data'])) {
                $processed++;
            } else {
                $remaining[] = $item;
            }
        }
        
        // Update queue file with remaining items
        file_put_contents($queueFile, json_encode($remaining, JSON_PRETTY_PRINT));
        
        return $processed;
    }
    
    private function loadTemplate(string $template, array $data = []): string
    {
        $templateFile = __DIR__ . '/../../views/emails/' . $template . '.php';
        
        if (!file_exists($templateFile)) {
            throw new \Exception("Email template not found: {$template}");
        }
        
        // Extract variables for template
        extract($data);
        
        // Start output buffering
        ob_start();
        include $templateFile;
        $content = ob_get_clean();
        
        return $content;
    }
    
    private function prepareHeaders(): array
    {
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . $this->config['mail']['from']['name'] . ' <' . $this->config['mail']['from']['address'] . '>',
            'Reply-To: ' . $this->config['mail']['from']['address'],
            'X-Mailer: AstroGenix Mailer',
            'X-Priority: 3',
            'X-MSMail-Priority: Normal'
        ];
        
        return $headers;
    }
    
    public function sendWelcomeEmail(string $email, string $name): bool
    {
        return $this->send($email, 'Welcome to AstroGenix!', 'welcome', [
            'name' => $name
        ]);
    }
    
    public function sendVerificationEmail(string $email, string $name, string $verificationUrl): bool
    {
        return $this->send($email, 'Verify Your Email - AstroGenix', 'verify-email', [
            'name' => $name,
            'verification_url' => $verificationUrl
        ]);
    }
    
    public function sendPasswordResetEmail(string $email, string $name, string $resetUrl): bool
    {
        return $this->send($email, 'Password Reset - AstroGenix', 'password-reset', [
            'name' => $name,
            'reset_url' => $resetUrl
        ]);
    }
    
    public function sendInvestmentNotification(string $email, string $name, array $investment): bool
    {
        return $this->send($email, 'Investment Created - AstroGenix', 'investment-created', [
            'name' => $name,
            'investment' => $investment
        ]);
    }
    
    public function sendProfitNotification(string $email, string $name, float $amount): bool
    {
        return $this->send($email, 'Daily Profit Credited - AstroGenix', 'profit-credited', [
            'name' => $name,
            'amount' => $amount
        ]);
    }
    
    public function sendWithdrawalNotification(string $email, string $name, array $withdrawal): bool
    {
        return $this->send($email, 'Withdrawal Processed - AstroGenix', 'withdrawal-processed', [
            'name' => $name,
            'withdrawal' => $withdrawal
        ]);
    }
    
    public function sendReferralNotification(string $email, string $name, float $commission): bool
    {
        return $this->send($email, 'Referral Commission Earned - AstroGenix', 'referral-commission', [
            'name' => $name,
            'commission' => $commission
        ]);
    }
    
    public function sendSupportTicketReply(string $email, string $name, array $ticket): bool
    {
        return $this->send($email, 'Support Ticket Reply - AstroGenix', 'support-reply', [
            'name' => $name,
            'ticket' => $ticket
        ]);
    }
    
    public function testConnection(): bool
    {
        try {
            return $this->sendRaw(
                $this->config['mail']['from']['address'],
                'Test Email - AstroGenix',
                'This is a test email to verify the mail configuration.'
            );
        } catch (\Exception $e) {
            return false;
        }
    }
}
