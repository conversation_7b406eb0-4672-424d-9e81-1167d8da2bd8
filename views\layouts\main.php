<!DOCTYPE html>
<html lang="<?= $language ?? 'en' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?= htmlspecialchars($meta_description ?? 'AstroGenix - Professional USDT Staking Platform') ?>">
    <meta name="keywords" content="USDT, staking, cryptocurrency, investment, passive income, DeFi">
    <meta name="author" content="AstroGenix">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?= htmlspecialchars($page_title ?? 'AstroGenix') ?>">
    <meta property="og:description" content="<?= htmlspecialchars($meta_description ?? 'Professional USDT Staking Platform') ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= url($_SERVER['REQUEST_URI']) ?>">
    <meta property="og:image" content="<?= asset('images/og-image.jpg') ?>">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?= htmlspecialchars($page_title ?? 'AstroGenix') ?>">
    <meta name="twitter:description" content="<?= htmlspecialchars($meta_description ?? 'Professional USDT Staking Platform') ?>">
    <meta name="twitter:image" content="<?= asset('images/og-image.jpg') ?>">
    
    <title><?= htmlspecialchars($page_title ?? 'AstroGenix - Professional USDT Staking Platform') ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= asset('images/favicon.ico') ?>">
    <link rel="apple-touch-icon" href="<?= asset('images/apple-touch-icon.png') ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="<?= asset('css/style.css') ?>" rel="stylesheet">
    
    <!-- Additional CSS -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link href="<?= asset($css) ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="<?= $body_class ?? '' ?>">
    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?= url('/') ?>">
                <img src="<?= asset('images/logo.png') ?>" alt="AstroGenix" height="40">
                <span class="ms-2 fw-bold">AstroGenix</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= url('/') ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= url('/packages') ?>">Investment Plans</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= url('/about') ?>">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= url('/news') ?>">News</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= url('/faq') ?>">FAQ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= url('/contact') ?>">Contact</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe"></i>
                            <?= ($language ?? 'en') === 'en' ? 'English' : 'Русский' ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= url('/lang/en') ?>">English</a></li>
                            <li><a class="dropdown-item" href="<?= url('/lang/ru') ?>">Русский</a></li>
                        </ul>
                    </li>
                    
                    <?php if (isset($user) && $user): ?>
                        <!-- User Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i>
                                <?= htmlspecialchars($user['username']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?= url('/dashboard') ?>">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a></li>
                                <li><a class="dropdown-item" href="<?= url('/dashboard/profile') ?>">
                                    <i class="fas fa-user me-2"></i>Profile
                                </a></li>
                                <li><a class="dropdown-item" href="<?= url('/dashboard/investments') ?>">
                                    <i class="fas fa-chart-line me-2"></i>Investments
                                </a></li>
                                <li><a class="dropdown-item" href="<?= url('/dashboard/transactions') ?>">
                                    <i class="fas fa-exchange-alt me-2"></i>Transactions
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= url('/logout') ?>">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <!-- Guest Menu -->
                        <li class="nav-item">
                            <a class="nav-link" href="<?= url('/login') ?>">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="<?= url('/register') ?>">Register</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <?php if (isset($session)): ?>
        <?php if ($success = $session->getSuccess()): ?>
            <div class="alert alert-success alert-dismissible fade show mt-5 pt-5" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error = $session->getError()): ?>
            <div class="alert alert-danger alert-dismissible fade show mt-5 pt-5" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?= htmlspecialchars($error) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($warning = $session->getWarning()): ?>
            <div class="alert alert-warning alert-dismissible fade show mt-5 pt-5" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($warning) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($info = $session->getInfo()): ?>
            <div class="alert alert-info alert-dismissible fade show mt-5 pt-5" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <?= htmlspecialchars($info) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content">
        <?= $content ?? '' ?>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">
                        <img src="<?= asset('images/logo.png') ?>" alt="AstroGenix" height="30" class="me-2">
                        AstroGenix
                    </h5>
                    <p class="text-muted">
                        Professional USDT staking platform offering secure and profitable investment opportunities 
                        with daily returns and transparent operations.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-telegram"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Platform</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?= url('/packages') ?>" class="text-muted">Investment Plans</a></li>
                        <li><a href="<?= url('/calculator') ?>" class="text-muted">Calculator</a></li>
                        <li><a href="<?= url('/about') ?>" class="text-muted">About Us</a></li>
                        <li><a href="<?= url('/news') ?>" class="text-muted">News</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Support</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?= url('/faq') ?>" class="text-muted">FAQ</a></li>
                        <li><a href="<?= url('/contact') ?>" class="text-muted">Contact</a></li>
                        <li><a href="<?= url('/dashboard/support') ?>" class="text-muted">Support Tickets</a></li>
                        <li><a href="#" class="text-muted">Live Chat</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Legal</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted">Terms of Service</a></li>
                        <li><a href="#" class="text-muted">Privacy Policy</a></li>
                        <li><a href="#" class="text-muted">Risk Disclosure</a></li>
                        <li><a href="#" class="text-muted">AML Policy</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">Contact Info</h6>
                    <ul class="list-unstyled text-muted">
                        <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i>******-ASTRO-GX</li>
                        <li><i class="fas fa-clock me-2"></i>24/7 Support</li>
                    </ul>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; <?= date('Y') ?> AstroGenix. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        SSL Secured | Licensed & Regulated
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="btn btn-primary back-to-top" title="Back to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="<?= asset('js/app.js') ?>"></script>
    
    <!-- Additional JavaScript -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?= asset($js) ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Inline JavaScript -->
    <?php if (isset($inline_js)): ?>
        <script><?= $inline_js ?></script>
    <?php endif; ?>
</body>
</html>
