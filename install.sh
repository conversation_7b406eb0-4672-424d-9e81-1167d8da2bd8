#!/bin/bash

# AstroGenix USDT Staking Platform - Automatic Server Installation Script
# This script will install and configure AstroGenix on Ubuntu/Debian servers
# Run with: sudo bash install.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration variables
DOMAIN=""
EMAIL=""
DB_PASSWORD=""
ADMIN_PASSWORD=""
USDT_WALLET=""
INSTALL_DIR="/var/www/astrogenix"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    🚀 AstroGenix Installer                   ║"
    echo "║              Professional USDT Staking Platform             ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to detect OS
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        print_error "Cannot detect operating system"
        exit 1
    fi
    
    print_status "Detected OS: $OS $VER"
    
    if [[ "$OS" != *"Ubuntu"* ]] && [[ "$OS" != *"Debian"* ]]; then
        print_warning "This script is designed for Ubuntu/Debian. Proceed with caution."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Function to collect user input
collect_input() {
    echo -e "${CYAN}Please provide the following information:${NC}"
    echo
    
    while [[ -z "$DOMAIN" ]]; do
        read -p "Domain name (e.g., astrogenix.com): " DOMAIN
        if [[ ! "$DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
            print_error "Invalid domain name format"
            DOMAIN=""
        fi
    done
    
    while [[ -z "$EMAIL" ]]; do
        read -p "Admin email address: " EMAIL
        if [[ ! "$EMAIL" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
            print_error "Invalid email format"
            EMAIL=""
        fi
    done
    
    while [[ -z "$DB_PASSWORD" ]]; do
        read -s -p "MySQL root password (will be created): " DB_PASSWORD
        echo
        if [[ ${#DB_PASSWORD} -lt 8 ]]; then
            print_error "Password must be at least 8 characters"
            DB_PASSWORD=""
        fi
    done
    
    while [[ -z "$ADMIN_PASSWORD" ]]; do
        read -s -p "Admin panel password: " ADMIN_PASSWORD
        echo
        if [[ ${#ADMIN_PASSWORD} -lt 8 ]]; then
            print_error "Password must be at least 8 characters"
            ADMIN_PASSWORD=""
        fi
    done
    
    while [[ -z "$USDT_WALLET" ]]; do
        read -p "USDT wallet address: " USDT_WALLET
        if [[ ${#USDT_WALLET} -lt 20 ]]; then
            print_error "Invalid USDT wallet address"
            USDT_WALLET=""
        fi
    done
    
    echo
    print_status "Configuration collected successfully"
}

# Function to update system
update_system() {
    print_status "Updating system packages..."
    apt update -y
    apt upgrade -y
    print_success "System updated"
}

# Function to install required packages
install_packages() {
    print_status "Installing required packages..."
    
    # Install basic packages
    apt install -y software-properties-common curl wget unzip git
    
    # Add PHP repository
    add-apt-repository ppa:ondrej/php -y
    apt update -y
    
    # Install Apache, PHP, MySQL
    apt install -y apache2 \
        php8.1 php8.1-mysql php8.1-curl php8.1-gd php8.1-mbstring \
        php8.1-xml php8.1-zip php8.1-bcmath php8.1-json php8.1-opcache \
        mysql-server \
        certbot python3-certbot-apache \
        ufw fail2ban htop
    
    print_success "Packages installed"
}

# Function to configure MySQL
configure_mysql() {
    print_status "Configuring MySQL..."
    
    # Secure MySQL installation
    mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$DB_PASSWORD';"
    mysql -u root -p$DB_PASSWORD -e "DELETE FROM mysql.user WHERE User='';"
    mysql -u root -p$DB_PASSWORD -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');"
    mysql -u root -p$DB_PASSWORD -e "DROP DATABASE IF EXISTS test;"
    mysql -u root -p$DB_PASSWORD -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';"
    mysql -u root -p$DB_PASSWORD -e "FLUSH PRIVILEGES;"
    
    # Create database and user
    mysql -u root -p$DB_PASSWORD -e "CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    mysql -u root -p$DB_PASSWORD -e "CREATE USER 'astrogenix_user'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
    mysql -u root -p$DB_PASSWORD -e "GRANT ALL PRIVILEGES ON astrogenix.* TO 'astrogenix_user'@'localhost';"
    mysql -u root -p$DB_PASSWORD -e "FLUSH PRIVILEGES;"
    
    print_success "MySQL configured"
}

# Function to configure PHP
configure_php() {
    print_status "Configuring PHP..."
    
    # Update PHP configuration
    sed -i 's/upload_max_filesize = .*/upload_max_filesize = 10M/' /etc/php/8.1/apache2/php.ini
    sed -i 's/post_max_size = .*/post_max_size = 10M/' /etc/php/8.1/apache2/php.ini
    sed -i 's/max_execution_time = .*/max_execution_time = 300/' /etc/php/8.1/apache2/php.ini
    sed -i 's/memory_limit = .*/memory_limit = 256M/' /etc/php/8.1/apache2/php.ini
    sed -i 's/;date.timezone.*/date.timezone = UTC/' /etc/php/8.1/apache2/php.ini
    
    # Enable OPcache
    sed -i 's/;opcache.enable=.*/opcache.enable=1/' /etc/php/8.1/apache2/php.ini
    sed -i 's/;opcache.memory_consumption=.*/opcache.memory_consumption=128/' /etc/php/8.1/apache2/php.ini
    sed -i 's/;opcache.interned_strings_buffer=.*/opcache.interned_strings_buffer=8/' /etc/php/8.1/apache2/php.ini
    sed -i 's/;opcache.max_accelerated_files=.*/opcache.max_accelerated_files=4000/' /etc/php/8.1/apache2/php.ini
    
    print_success "PHP configured"
}

# Function to configure Apache
configure_apache() {
    print_status "Configuring Apache..."
    
    # Enable required modules
    a2enmod rewrite
    a2enmod ssl
    a2enmod headers
    
    # Create virtual host
    cat > /etc/apache2/sites-available/astrogenix.conf << EOF
<VirtualHost *:80>
    ServerName $DOMAIN
    ServerAlias www.$DOMAIN
    DocumentRoot $INSTALL_DIR/public
    
    <Directory $INSTALL_DIR/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog \${APACHE_LOG_DIR}/astrogenix_error.log
    CustomLog \${APACHE_LOG_DIR}/astrogenix_access.log combined
</VirtualHost>
EOF
    
    # Enable site
    a2ensite astrogenix.conf
    a2dissite 000-default.conf
    
    print_success "Apache configured"
}

# Function to install AstroGenix
install_astrogenix() {
    print_status "Installing AstroGenix..."
    
    # Create installation directory
    mkdir -p $INSTALL_DIR
    
    # Copy files (assuming they're in current directory)
    if [[ -f "public/index.php" ]]; then
        cp -r . $INSTALL_DIR/
    else
        print_error "AstroGenix files not found in current directory"
        print_status "Please ensure you're running this script from the AstroGenix directory"
        exit 1
    fi
    
    # Set permissions
    chown -R www-data:www-data $INSTALL_DIR
    chmod -R 755 $INSTALL_DIR
    chmod -R 777 $INSTALL_DIR/storage
    chmod -R 777 $INSTALL_DIR/public/uploads
    
    # Create .env file
    cat > $INSTALL_DIR/.env << EOF
APP_NAME="AstroGenix"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://$DOMAIN
APP_KEY=$(openssl rand -base64 32)

DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=astrogenix
DB_USERNAME=astrogenix_user
DB_PASSWORD=$DB_PASSWORD

MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=$EMAIL
MAIL_PASSWORD=
MAIL_FROM_ADDRESS=$EMAIL
MAIL_FROM_NAME="AstroGenix"

USDT_WALLET_ADDRESS=$USDT_WALLET
EOF
    
    chmod 644 $INSTALL_DIR/.env
    
    print_success "AstroGenix installed"
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    # Import schema
    if [[ -f "$INSTALL_DIR/database/schema.sql" ]]; then
        mysql -u astrogenix_user -p$DB_PASSWORD astrogenix < $INSTALL_DIR/database/schema.sql
    fi
    
    # Import seed data
    if [[ -f "$INSTALL_DIR/database/seed_data.sql" ]]; then
        mysql -u astrogenix_user -p$DB_PASSWORD astrogenix < $INSTALL_DIR/database/seed_data.sql
    fi
    
    # Update admin user
    ADMIN_HASH=$(php -r "echo password_hash('$ADMIN_PASSWORD', PASSWORD_DEFAULT);")
    mysql -u astrogenix_user -p$DB_PASSWORD astrogenix -e "UPDATE admin_users SET email='$EMAIL', password_hash='$ADMIN_HASH' WHERE id=1;"
    
    print_success "Database setup complete"
}

# Function to configure SSL
configure_ssl() {
    print_status "Configuring SSL certificate..."
    
    # Restart Apache
    systemctl restart apache2
    
    # Get SSL certificate
    certbot --apache -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email $EMAIL --redirect
    
    print_success "SSL certificate configured"
}

# Function to configure firewall
configure_firewall() {
    print_status "Configuring firewall..."
    
    # Configure UFW
    ufw --force reset
    ufw default deny incoming
    ufw default allow outgoing
    ufw allow ssh
    ufw allow 'Apache Full'
    ufw --force enable
    
    print_success "Firewall configured"
}

# Function to setup cron jobs
setup_cron() {
    print_status "Setting up cron jobs..."
    
    # Create cron jobs for www-data user
    cat > /tmp/astrogenix-cron << EOF
# AstroGenix Cron Jobs
0 0 * * * /usr/bin/php $INSTALL_DIR/cron/distribute_profits.php
0 * * * * /usr/bin/php $INSTALL_DIR/cron/process_transactions.php
*/15 * * * * /usr/bin/php $INSTALL_DIR/cron/send_notifications.php
0 2 * * * /usr/bin/php $INSTALL_DIR/cron/cleanup_sessions.php
0 3 * * * /usr/bin/mysqldump -u astrogenix_user -p$DB_PASSWORD astrogenix > /var/backups/astrogenix_\$(date +\\%Y\\%m\\%d).sql
EOF
    
    # Install cron jobs
    crontab -u www-data /tmp/astrogenix-cron
    rm /tmp/astrogenix-cron
    
    # Create backup directory
    mkdir -p /var/backups
    chown www-data:www-data /var/backups
    
    print_success "Cron jobs configured"
}

# Function to optimize system
optimize_system() {
    print_status "Optimizing system..."
    
    # Configure MySQL
    cat >> /etc/mysql/mysql.conf.d/mysqld.cnf << EOF

# AstroGenix Optimizations
innodb_buffer_pool_size = 256M
query_cache_type = 1
query_cache_size = 64M
max_connections = 200
EOF
    
    # Restart services
    systemctl restart mysql
    systemctl restart apache2
    
    # Enable services
    systemctl enable apache2
    systemctl enable mysql
    
    print_success "System optimized"
}

# Function to create installation marker
create_marker() {
    echo "$(date '+%Y-%m-%d %H:%M:%S')" > $INSTALL_DIR/INSTALLED
    chown www-data:www-data $INSTALL_DIR/INSTALLED
}

# Function to display final information
show_completion() {
    echo
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                 🎉 Installation Complete! 🎉                ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${CYAN}Your AstroGenix platform is now ready!${NC}"
    echo
    echo -e "${YELLOW}Website:${NC} https://$DOMAIN"
    echo -e "${YELLOW}Admin Panel:${NC} https://$DOMAIN/admin"
    echo -e "${YELLOW}Admin Email:${NC} $EMAIL"
    echo -e "${YELLOW}Admin Password:${NC} $ADMIN_PASSWORD"
    echo
    echo -e "${CYAN}Important Security Notes:${NC}"
    echo "• Change your admin password after first login"
    echo "• Configure your email SMTP settings"
    echo "• Update your USDT wallet address in admin settings"
    echo "• Delete the install.php file from your server"
    echo "• Monitor your server logs regularly"
    echo
    echo -e "${CYAN}Next Steps:${NC}"
    echo "1. Visit your website and test functionality"
    echo "2. Configure investment packages in admin panel"
    echo "3. Set up email templates"
    echo "4. Test deposit and withdrawal processes"
    echo "5. Configure additional security measures"
    echo
    echo -e "${GREEN}Thank you for choosing AstroGenix! 🚀${NC}"
}

# Main installation function
main() {
    print_header
    
    check_root
    detect_os
    collect_input
    
    print_status "Starting installation process..."
    
    update_system
    install_packages
    configure_mysql
    configure_php
    configure_apache
    install_astrogenix
    setup_database
    configure_ssl
    configure_firewall
    setup_cron
    optimize_system
    create_marker
    
    show_completion
}

# Error handling
trap 'print_error "Installation failed at line $LINENO. Check the logs for details."' ERR

# Run main function
main "$@"
