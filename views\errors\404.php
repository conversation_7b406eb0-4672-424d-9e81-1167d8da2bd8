<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | AstroGenix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
        }
        .error-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 2rem;
        }
        .error-code {
            font-size: 8rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 1rem;
            text-shadow: 0 0 30px rgba(255,255,255,0.3);
        }
        .error-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        .error-description {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .btn-home {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .btn-home:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            color: white;
            transform: translateY(-2px);
        }
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        .shape {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .search-box {
            margin: 2rem 0;
        }
        .search-input {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 50px;
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }
        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
        }
        .search-input:focus {
            outline: none;
            border-color: rgba(255,255,255,0.5);
            background: rgba(255,255,255,0.3);
        }
        .quick-links {
            margin-top: 2rem;
        }
        .quick-links a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            margin: 0 15px;
            font-size: 1rem;
            transition: color 0.3s ease;
        }
        .quick-links a:hover {
            color: white;
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>
    
    <div class="error-container">
        <div class="error-code">404</div>
        <h1 class="error-title">Oops! Page Not Found</h1>
        <p class="error-description">
            The page you're looking for seems to have drifted away into the crypto cosmos. 
            Don't worry, let's get you back on track to your USDT staking journey!
        </p>
        
        <div class="search-box">
            <input type="text" class="search-input" placeholder="🔍 Search for investment plans, news, or help..." id="searchInput">
        </div>
        
        <div class="mb-4">
            <a href="/" class="btn-home">
                <i class="fas fa-home me-2"></i>
                Return to Home
            </a>
        </div>
        
        <div class="quick-links">
            <a href="/packages">
                <i class="fas fa-chart-line me-1"></i>
                Investment Plans
            </a>
            <a href="/dashboard">
                <i class="fas fa-tachometer-alt me-1"></i>
                Dashboard
            </a>
            <a href="/news">
                <i class="fas fa-newspaper me-1"></i>
                News
            </a>
            <a href="/contact">
                <i class="fas fa-headset me-1"></i>
                Support
            </a>
        </div>
        
        <div class="mt-4">
            <small style="opacity: 0.7;">
                <i class="fas fa-shield-alt me-1"></i>
                Your investments are safe and secure with AstroGenix
            </small>
        </div>
    </div>

    <script>
        // Simple search functionality
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    // Redirect to search or relevant page based on query
                    if (query.toLowerCase().includes('invest') || query.toLowerCase().includes('plan')) {
                        window.location.href = '/packages';
                    } else if (query.toLowerCase().includes('news')) {
                        window.location.href = '/news';
                    } else if (query.toLowerCase().includes('support') || query.toLowerCase().includes('help')) {
                        window.location.href = '/contact';
                    } else if (query.toLowerCase().includes('dashboard')) {
                        window.location.href = '/dashboard';
                    } else {
                        window.location.href = '/';
                    }
                }
            }
        });

        // Add some interactive effects
        document.addEventListener('mousemove', function(e) {
            const shapes = document.querySelectorAll('.shape');
            const x = e.clientX / window.innerWidth;
            const y = e.clientY / window.innerHeight;
            
            shapes.forEach((shape, index) => {
                const speed = (index + 1) * 0.5;
                const xPos = x * speed * 10;
                const yPos = y * speed * 10;
                shape.style.transform = `translate(${xPos}px, ${yPos}px)`;
            });
        });
    </script>
</body>
</html>
