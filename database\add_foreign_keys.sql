-- Добавление внешних ключей для AstroGenix
USE `astrogenix`;

-- Внешние ключи для таблицы users
ALTER TABLE `users`
  ADD CONSTRAINT `users_referred_by_fk` FOREIGN KEY (`referred_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- Внешние ключи для таблицы user_investments
ALTER TABLE `user_investments`
  ADD CONSTRAINT `user_investments_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_investments_package_fk` FOREIGN KEY (`package_id`) REFERENCES `investment_packages` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы daily_profits
ALTER TABLE `daily_profits`
  ADD CONSTRAINT `daily_profits_investment_fk` FOREIGN KEY (`investment_id`) REFERENCES `user_investments` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `daily_profits_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы transactions
ALTER TABLE `transactions`
  ADD CONSTRAINT `transactions_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `transactions_processed_by_fk` FOREIGN KEY (`processed_by`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

-- Внешние ключи для таблицы referral_commissions
ALTER TABLE `referral_commissions`
  ADD CONSTRAINT `referral_commissions_referrer_fk` FOREIGN KEY (`referrer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `referral_commissions_referred_fk` FOREIGN KEY (`referred_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `referral_commissions_investment_fk` FOREIGN KEY (`investment_id`) REFERENCES `user_investments` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы user_tasks
ALTER TABLE `user_tasks`
  ADD CONSTRAINT `user_tasks_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_tasks_task_fk` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы news
ALTER TABLE `news`
  ADD CONSTRAINT `news_category_fk` FOREIGN KEY (`category_id`) REFERENCES `news_categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `news_author_fk` FOREIGN KEY (`author_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE;

-- Внешние ключи для таблицы support_tickets
ALTER TABLE `support_tickets`
  ADD CONSTRAINT `support_tickets_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `support_tickets_assigned_fk` FOREIGN KEY (`assigned_to`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

-- Внешние ключи для таблицы support_messages
ALTER TABLE `support_messages`
  ADD CONSTRAINT `support_messages_ticket_fk` FOREIGN KEY (`ticket_id`) REFERENCES `support_tickets` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `support_messages_sender_fk` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
