<VirtualHost *:80>
    ServerAdmin <EMAIL>
    DocumentRoot /var/www/html/public
    
    <Directory /var/www/html/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Security Headers
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Referrer-Policy "strict-origin-when-cross-origin"
        Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
        
        # HSTS (uncomment for HTTPS)
        # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    </Directory>
    
    # Deny access to sensitive files
    <FilesMatch "\.(env|log|sql|md|txt|yml|yaml|json|lock)$">
        Require all denied
    </FilesMatch>
    
    # Deny access to directories
    <DirectoryMatch "/(config|database|src|storage|vendor|\.git)">
        Require all denied
    </DirectoryMatch>
    
    # Enable compression
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
        AddOutputFilterByType DEFLATE application/json
    </IfModule>
    
    # Browser caching
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/pdf "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType application/x-javascript "access plus 1 month"
        ExpiresByType application/x-shockwave-flash "access plus 1 month"
        ExpiresByType image/x-icon "access plus 1 year"
        ExpiresDefault "access plus 2 days"
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/astrogenix_error.log
    CustomLog ${APACHE_LOG_DIR}/astrogenix_access.log combined
</VirtualHost>

# SSL Configuration (uncomment and configure for HTTPS)
# <VirtualHost *:443>
#     ServerAdmin <EMAIL>
#     DocumentRoot /var/www/html/public
#     
#     SSLEngine on
#     SSLCertificateFile /etc/ssl/certs/astrogenix.crt
#     SSLCertificateKeyFile /etc/ssl/private/astrogenix.key
#     
#     <Directory /var/www/html/public>
#         Options -Indexes +FollowSymLinks
#         AllowOverride All
#         Require all granted
#         
#         # Security Headers for HTTPS
#         Header always set X-Content-Type-Options nosniff
#         Header always set X-Frame-Options DENY
#         Header always set X-XSS-Protection "1; mode=block"
#         Header always set Referrer-Policy "strict-origin-when-cross-origin"
#         Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
#         Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
#     </Directory>
#     
#     ErrorLog ${APACHE_LOG_DIR}/astrogenix_ssl_error.log
#     CustomLog ${APACHE_LOG_DIR}/astrogenix_ssl_access.log combined
# </VirtualHost>
