<?php

namespace AstroGenix\Core;

/**
 * Input Validation Class
 */
class Validator
{
    private $errors = [];
    private $data = [];
    
    public function validate(array $data, array $rules): array
    {
        $this->data = $data;
        $this->errors = [];
        
        foreach ($rules as $field => $ruleString) {
            $this->validateField($field, $ruleString);
        }
        
        return [
            'valid' => empty($this->errors),
            'errors' => $this->errors,
            'data' => $this->data
        ];
    }
    
    private function validateField(string $field, string $ruleString): void
    {
        $rules = explode('|', $ruleString);
        $value = $this->data[$field] ?? null;
        
        foreach ($rules as $rule) {
            $this->applyRule($field, $value, $rule);
        }
    }
    
    private function applyRule(string $field, $value, string $rule): void
    {
        $parts = explode(':', $rule, 2);
        $ruleName = $parts[0];
        $parameter = $parts[1] ?? null;
        
        switch ($ruleName) {
            case 'required':
                if (empty($value) && $value !== '0') {
                    $this->addError($field, 'This field is required');
                }
                break;
                
            case 'email':
                if ($value && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $this->addError($field, 'Please enter a valid email address');
                }
                break;
                
            case 'min':
                if ($value && strlen($value) < (int)$parameter) {
                    $this->addError($field, "Minimum length is {$parameter} characters");
                }
                break;
                
            case 'max':
                if ($value && strlen($value) > (int)$parameter) {
                    $this->addError($field, "Maximum length is {$parameter} characters");
                }
                break;
                
            case 'numeric':
                if ($value && !is_numeric($value)) {
                    $this->addError($field, 'This field must be a number');
                }
                break;
                
            case 'integer':
                if ($value && !filter_var($value, FILTER_VALIDATE_INT)) {
                    $this->addError($field, 'This field must be an integer');
                }
                break;
                
            case 'alpha':
                if ($value && !ctype_alpha($value)) {
                    $this->addError($field, 'This field may only contain letters');
                }
                break;
                
            case 'alpha_num':
                if ($value && !ctype_alnum($value)) {
                    $this->addError($field, 'This field may only contain letters and numbers');
                }
                break;
                
            case 'url':
                if ($value && !filter_var($value, FILTER_VALIDATE_URL)) {
                    $this->addError($field, 'Please enter a valid URL');
                }
                break;
                
            case 'same':
                $otherField = $parameter;
                $otherValue = $this->data[$otherField] ?? null;
                if ($value !== $otherValue) {
                    $this->addError($field, "This field must match {$otherField}");
                }
                break;
                
            case 'different':
                $otherField = $parameter;
                $otherValue = $this->data[$otherField] ?? null;
                if ($value === $otherValue) {
                    $this->addError($field, "This field must be different from {$otherField}");
                }
                break;
                
            case 'in':
                $allowedValues = explode(',', $parameter);
                if ($value && !in_array($value, $allowedValues)) {
                    $this->addError($field, 'Please select a valid option');
                }
                break;
                
            case 'not_in':
                $forbiddenValues = explode(',', $parameter);
                if ($value && in_array($value, $forbiddenValues)) {
                    $this->addError($field, 'The selected value is not allowed');
                }
                break;
                
            case 'regex':
                if ($value && !preg_match($parameter, $value)) {
                    $this->addError($field, 'This field format is invalid');
                }
                break;
                
            case 'date':
                if ($value && !strtotime($value)) {
                    $this->addError($field, 'Please enter a valid date');
                }
                break;
                
            case 'before':
                if ($value && strtotime($value) >= strtotime($parameter)) {
                    $this->addError($field, "Date must be before {$parameter}");
                }
                break;
                
            case 'after':
                if ($value && strtotime($value) <= strtotime($parameter)) {
                    $this->addError($field, "Date must be after {$parameter}");
                }
                break;
                
            case 'unique':
                // Database uniqueness check
                if ($value && $this->checkUnique($field, $value, $parameter)) {
                    $this->addError($field, 'This value is already taken');
                }
                break;
                
            case 'exists':
                // Database existence check
                if ($value && !$this->checkExists($field, $value, $parameter)) {
                    $this->addError($field, 'The selected value is invalid');
                }
                break;
                
            case 'confirmed':
                $confirmField = $field . '_confirmation';
                $confirmValue = $this->data[$confirmField] ?? null;
                if ($value !== $confirmValue) {
                    $this->addError($field, 'Password confirmation does not match');
                }
                break;
                
            case 'file':
                if (isset($_FILES[$field]) && $_FILES[$field]['error'] !== UPLOAD_ERR_OK) {
                    $this->addError($field, 'File upload failed');
                }
                break;
                
            case 'image':
                if (isset($_FILES[$field]) && $_FILES[$field]['error'] === UPLOAD_ERR_OK) {
                    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                    $fileType = $_FILES[$field]['type'];
                    if (!in_array($fileType, $allowedTypes)) {
                        $this->addError($field, 'File must be an image (JPEG, PNG, GIF, WebP)');
                    }
                }
                break;
                
            case 'mimes':
                if (isset($_FILES[$field]) && $_FILES[$field]['error'] === UPLOAD_ERR_OK) {
                    $allowedMimes = explode(',', $parameter);
                    $fileType = $_FILES[$field]['type'];
                    if (!in_array($fileType, $allowedMimes)) {
                        $this->addError($field, 'File type not allowed');
                    }
                }
                break;
                
            case 'max_size':
                if (isset($_FILES[$field]) && $_FILES[$field]['error'] === UPLOAD_ERR_OK) {
                    $maxSize = (int)$parameter * 1024; // Convert KB to bytes
                    if ($_FILES[$field]['size'] > $maxSize) {
                        $this->addError($field, "File size must not exceed {$parameter}KB");
                    }
                }
                break;
        }
    }
    
    private function addError(string $field, string $message): void
    {
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        $this->errors[$field][] = $message;
    }
    
    private function checkUnique(string $field, $value, string $table): bool
    {
        try {
            $db = Database::getInstance();
            $result = $db->fetch("SELECT COUNT(*) as count FROM {$table} WHERE {$field} = :value", [
                'value' => $value
            ]);
            return $result['count'] > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    private function checkExists(string $field, $value, string $table): bool
    {
        try {
            $db = Database::getInstance();
            $result = $db->fetch("SELECT COUNT(*) as count FROM {$table} WHERE {$field} = :value", [
                'value' => $value
            ]);
            return $result['count'] > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    public function getErrors(): array
    {
        return $this->errors;
    }
    
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }
    
    public function getFirstError(string $field): ?string
    {
        return $this->errors[$field][0] ?? null;
    }
    
    public function getAllErrors(): array
    {
        $allErrors = [];
        foreach ($this->errors as $field => $errors) {
            $allErrors = array_merge($allErrors, $errors);
        }
        return $allErrors;
    }
    
    public static function make(array $data, array $rules): array
    {
        $validator = new self();
        return $validator->validate($data, $rules);
    }
}
