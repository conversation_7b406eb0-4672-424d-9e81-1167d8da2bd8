<?php

namespace AstroGenix\Core;

/**
 * Session Management Class
 */
class Session
{
    private $started = false;
    
    public function __construct()
    {
        $this->start();
    }
    
    public function start(): void
    {
        if ($this->started || session_status() === PHP_SESSION_ACTIVE) {
            return;
        }
        
        // Configure session settings
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Lax');
        
        session_name('astrogenix_session');
        session_start();
        
        $this->started = true;
        
        // Regenerate session ID periodically for security
        if (!$this->has('_session_started')) {
            session_regenerate_id(true);
            $this->set('_session_started', time());
        } elseif (time() - $this->get('_session_started') > 1800) { // 30 minutes
            session_regenerate_id(true);
            $this->set('_session_started', time());
        }
    }
    
    public function get(string $key, $default = null)
    {
        return $_SESSION[$key] ?? $default;
    }
    
    public function set(string $key, $value): void
    {
        $_SESSION[$key] = $value;
    }
    
    public function has(string $key): bool
    {
        return isset($_SESSION[$key]);
    }
    
    public function remove(string $key): void
    {
        unset($_SESSION[$key]);
    }
    
    public function clear(): void
    {
        $_SESSION = [];
    }
    
    public function destroy(): void
    {
        if ($this->started) {
            session_destroy();
            $this->started = false;
        }
    }
    
    public function regenerate(): void
    {
        session_regenerate_id(true);
        $this->set('_session_started', time());
    }
    
    public function getId(): string
    {
        return session_id();
    }
    
    public function setFlash(string $key, $value): void
    {
        $this->set('_flash_' . $key, $value);
    }
    
    public function getFlash(string $key, $default = null)
    {
        $value = $this->get('_flash_' . $key, $default);
        $this->remove('_flash_' . $key);
        return $value;
    }
    
    public function hasFlash(string $key): bool
    {
        return $this->has('_flash_' . $key);
    }
    
    public function setSuccess(string $message): void
    {
        $this->setFlash('success', $message);
    }
    
    public function setError(string $message): void
    {
        $this->setFlash('error', $message);
    }
    
    public function setWarning(string $message): void
    {
        $this->setFlash('warning', $message);
    }
    
    public function setInfo(string $message): void
    {
        $this->setFlash('info', $message);
    }
    
    public function getSuccess(): ?string
    {
        return $this->getFlash('success');
    }
    
    public function getError(): ?string
    {
        return $this->getFlash('error');
    }
    
    public function getWarning(): ?string
    {
        return $this->getFlash('warning');
    }
    
    public function getInfo(): ?string
    {
        return $this->getFlash('info');
    }
    
    public function keepFlash(array $keys = []): void
    {
        if (empty($keys)) {
            // Keep all flash messages
            foreach ($_SESSION as $key => $value) {
                if (strpos($key, '_flash_') === 0) {
                    $newKey = str_replace('_flash_', '_flash_keep_', $key);
                    $this->set($newKey, $value);
                }
            }
        } else {
            // Keep specific flash messages
            foreach ($keys as $key) {
                if ($this->hasFlash($key)) {
                    $value = $this->get('_flash_' . $key);
                    $this->set('_flash_keep_' . $key, $value);
                }
            }
        }
    }
    
    public function reflash(): void
    {
        foreach ($_SESSION as $key => $value) {
            if (strpos($key, '_flash_keep_') === 0) {
                $newKey = str_replace('_flash_keep_', '_flash_', $key);
                $this->set($newKey, $value);
                $this->remove($key);
            }
        }
    }
    
    public function increment(string $key, int $amount = 1): int
    {
        $value = $this->get($key, 0) + $amount;
        $this->set($key, $value);
        return $value;
    }
    
    public function decrement(string $key, int $amount = 1): int
    {
        $value = $this->get($key, 0) - $amount;
        $this->set($key, $value);
        return $value;
    }
    
    public function push(string $key, $value): void
    {
        $array = $this->get($key, []);
        if (!is_array($array)) {
            $array = [];
        }
        $array[] = $value;
        $this->set($key, $array);
    }
    
    public function pull(string $key, $default = null)
    {
        $value = $this->get($key, $default);
        $this->remove($key);
        return $value;
    }
    
    public function forget(array $keys): void
    {
        foreach ($keys as $key) {
            $this->remove($key);
        }
    }
    
    public function flush(): void
    {
        $this->clear();
    }
    
    public function all(): array
    {
        return $_SESSION;
    }
    
    public function token(): string
    {
        if (!$this->has('_token')) {
            $this->set('_token', bin2hex(random_bytes(32)));
        }
        return $this->get('_token');
    }
    
    public function isValidToken(string $token): bool
    {
        return hash_equals($this->token(), $token);
    }
    
    public function previousUrl(): ?string
    {
        return $this->get('_previous_url');
    }
    
    public function setPreviousUrl(string $url): void
    {
        $this->set('_previous_url', $url);
    }
    
    public function intended(string $default = '/'): string
    {
        return $this->pull('_intended_url', $default);
    }
    
    public function setIntended(string $url): void
    {
        $this->set('_intended_url', $url);
    }
    
    public function isExpired(): bool
    {
        $lastActivity = $this->get('_last_activity', 0);
        $timeout = 7200; // 2 hours
        
        return (time() - $lastActivity) > $timeout;
    }
    
    public function updateActivity(): void
    {
        $this->set('_last_activity', time());
    }
    
    public function getAge(): int
    {
        return time() - $this->get('_session_started', time());
    }
    
    public function isStarted(): bool
    {
        return $this->started;
    }
}
