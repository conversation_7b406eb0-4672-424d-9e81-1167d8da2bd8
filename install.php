<?php
/**
 * AstroGenix USDT Staking Platform - Automatic Installer
 * 
 * This script will automatically install and configure the AstroGenix platform
 * Run this script in your browser: http://yourdomain.com/install.php
 */

// Prevent running if already installed
if (file_exists('.env') && file_exists('INSTALLED')) {
    die('AstroGenix is already installed! Delete the INSTALLED file to reinstall.');
}

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Installation steps
$steps = [
    1 => 'System Requirements Check',
    2 => 'Database Configuration',
    3 => 'Application Settings',
    4 => 'Admin Account Setup',
    5 => 'Final Configuration',
    6 => 'Installation Complete'
];

$currentStep = isset($_GET['step']) ? (int)$_GET['step'] : 1;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroGenix Installation Wizard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        .installer-container {
            max-width: 800px;
            margin: 2rem auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .installer-header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 2rem 0;
            padding: 0 2rem;
        }
        .step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-weight: bold;
        }
        .step.active .step-number {
            background: #6366f1;
            color: white;
        }
        .step.completed .step-number {
            background: #10b981;
            color: white;
        }
        .step-line {
            position: absolute;
            top: 20px;
            left: 50%;
            width: 100%;
            height: 2px;
            background: #e9ecef;
            z-index: -1;
        }
        .step:last-child .step-line {
            display: none;
        }
        .installer-content {
            padding: 2rem;
        }
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #e9ecef;
        }
        .requirement-item:last-child {
            border-bottom: none;
        }
        .status-ok {
            color: #10b981;
        }
        .status-error {
            color: #ef4444;
        }
        .status-warning {
            color: #f59e0b;
        }
    </style>
</head>
<body>
    <div class="installer-container">
        <div class="installer-header">
            <h1><i class="fas fa-rocket me-2"></i>AstroGenix</h1>
            <p class="mb-0">Professional USDT Staking Platform - Installation Wizard</p>
        </div>

        <!-- Step Indicator -->
        <div class="step-indicator">
            <?php foreach ($steps as $num => $title): ?>
                <div class="step <?= $num < $currentStep ? 'completed' : ($num == $currentStep ? 'active' : '') ?>">
                    <div class="step-number"><?= $num ?></div>
                    <small><?= $title ?></small>
                    <div class="step-line"></div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="installer-content">
            <?php
            switch ($currentStep) {
                case 1:
                    showSystemRequirements();
                    break;
                case 2:
                    showDatabaseConfig();
                    break;
                case 3:
                    showApplicationSettings();
                    break;
                case 4:
                    showAdminSetup();
                    break;
                case 5:
                    showFinalConfiguration();
                    break;
                case 6:
                    showInstallationComplete();
                    break;
                default:
                    showSystemRequirements();
            }
            ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php

function showSystemRequirements() {
    $requirements = checkSystemRequirements();
    $canProceed = $requirements['can_proceed'];
    ?>
    <h3><i class="fas fa-server me-2"></i>System Requirements Check</h3>
    <p class="text-muted">Checking if your server meets the minimum requirements for AstroGenix.</p>

    <div class="requirements-list">
        <?php foreach ($requirements['checks'] as $check): ?>
            <div class="requirement-item">
                <div>
                    <strong><?= $check['name'] ?></strong>
                    <br><small class="text-muted"><?= $check['description'] ?></small>
                </div>
                <div class="<?= $check['status'] == 'ok' ? 'status-ok' : ($check['status'] == 'warning' ? 'status-warning' : 'status-error') ?>">
                    <i class="fas fa-<?= $check['status'] == 'ok' ? 'check-circle' : ($check['status'] == 'warning' ? 'exclamation-triangle' : 'times-circle') ?>"></i>
                    <?= $check['message'] ?>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <?php if ($canProceed): ?>
        <div class="alert alert-success mt-3">
            <i class="fas fa-check-circle me-2"></i>
            Great! Your server meets all requirements. You can proceed with the installation.
        </div>
        <div class="text-end">
            <a href="?step=2" class="btn btn-primary">
                Next: Database Configuration <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    <?php else: ?>
        <div class="alert alert-danger mt-3">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Your server doesn't meet some requirements. Please fix the issues above before proceeding.
        </div>
        <div class="text-end">
            <a href="?step=1" class="btn btn-secondary">
                <i class="fas fa-redo me-2"></i>Check Again
            </a>
        </div>
    <?php endif; ?>
    <?php
}

function showDatabaseConfig() {
    if ($_POST) {
        $result = processDatabaseConfig($_POST);
        if ($result['success']) {
            $_SESSION['db_config'] = $_POST;
            echo '<script>window.location.href = "?step=3";</script>';
            return;
        } else {
            echo '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>' . $result['message'] . '</div>';
        }
    }
    ?>
    <h3><i class="fas fa-database me-2"></i>Database Configuration</h3>
    <p class="text-muted">Configure your MySQL database connection settings.</p>

    <form method="POST">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Database Host</label>
                    <input type="text" class="form-control" name="db_host" value="<?= $_POST['db_host'] ?? 'localhost' ?>" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Database Port</label>
                    <input type="number" class="form-control" name="db_port" value="<?= $_POST['db_port'] ?? '3306' ?>" required>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label class="form-label">Database Name</label>
            <input type="text" class="form-control" name="db_name" value="<?= $_POST['db_name'] ?? 'astrogenix' ?>" required>
            <div class="form-text">The database will be created if it doesn't exist.</div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Database Username</label>
                    <input type="text" class="form-control" name="db_username" value="<?= $_POST['db_username'] ?? '' ?>" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Database Password</label>
                    <input type="password" class="form-control" name="db_password" value="<?= $_POST['db_password'] ?? '' ?>">
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between">
            <a href="?step=1" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Previous
            </a>
            <button type="submit" class="btn btn-primary">
                Test Connection & Continue <i class="fas fa-arrow-right ms-2"></i>
            </button>
        </div>
    </form>
    <?php
}

function showApplicationSettings() {
    if ($_POST) {
        $_SESSION['app_config'] = $_POST;
        echo '<script>window.location.href = "?step=4";</script>';
        return;
    }
    ?>
    <h3><i class="fas fa-cog me-2"></i>Application Settings</h3>
    <p class="text-muted">Configure your application settings and email configuration.</p>

    <form method="POST">
        <div class="mb-4">
            <h5>Basic Settings</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Site URL</label>
                        <input type="url" class="form-control" name="app_url" value="<?= $_POST['app_url'] ?? 'http://' . $_SERVER['HTTP_HOST'] ?>" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Site Name</label>
                        <input type="text" class="form-control" name="app_name" value="<?= $_POST['app_name'] ?? 'AstroGenix' ?>" required>
                    </div>
                </div>
            </div>
        </div>

        <div class="mb-4">
            <h5>Email Configuration</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">SMTP Host</label>
                        <input type="text" class="form-control" name="mail_host" value="<?= $_POST['mail_host'] ?? 'smtp.gmail.com' ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">SMTP Port</label>
                        <input type="number" class="form-control" name="mail_port" value="<?= $_POST['mail_port'] ?? '587' ?>">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">SMTP Username</label>
                        <input type="email" class="form-control" name="mail_username" value="<?= $_POST['mail_username'] ?? '' ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">SMTP Password</label>
                        <input type="password" class="form-control" name="mail_password" value="<?= $_POST['mail_password'] ?? '' ?>">
                    </div>
                </div>
            </div>
        </div>

        <div class="mb-4">
            <h5>USDT Wallet</h5>
            <div class="mb-3">
                <label class="form-label">USDT Wallet Address</label>
                <input type="text" class="form-control" name="usdt_wallet" value="<?= $_POST['usdt_wallet'] ?? '' ?>" required>
                <div class="form-text">Your USDT wallet address for receiving deposits.</div>
            </div>
        </div>

        <div class="d-flex justify-content-between">
            <a href="?step=2" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Previous
            </a>
            <button type="submit" class="btn btn-primary">
                Continue <i class="fas fa-arrow-right ms-2"></i>
            </button>
        </div>
    </form>
    <?php
}

function showAdminSetup() {
    if ($_POST) {
        $result = processAdminSetup($_POST);
        if ($result['success']) {
            $_SESSION['admin_config'] = $_POST;
            echo '<script>window.location.href = "?step=5";</script>';
            return;
        } else {
            echo '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>' . $result['message'] . '</div>';
        }
    }
    ?>
    <h3><i class="fas fa-user-shield me-2"></i>Admin Account Setup</h3>
    <p class="text-muted">Create your administrator account to manage the platform.</p>

    <form method="POST">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Full Name</label>
                    <input type="text" class="form-control" name="admin_name" value="<?= $_POST['admin_name'] ?? '' ?>" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Username</label>
                    <input type="text" class="form-control" name="admin_username" value="<?= $_POST['admin_username'] ?? 'admin' ?>" required>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label class="form-label">Email Address</label>
            <input type="email" class="form-control" name="admin_email" value="<?= $_POST['admin_email'] ?? '' ?>" required>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <input type="password" class="form-control" name="admin_password" required>
                    <div class="form-text">Minimum 8 characters with letters and numbers.</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">Confirm Password</label>
                    <input type="password" class="form-control" name="admin_password_confirm" required>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between">
            <a href="?step=3" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Previous
            </a>
            <button type="submit" class="btn btn-primary">
                Create Admin Account <i class="fas fa-arrow-right ms-2"></i>
            </button>
        </div>
    </form>
    <?php
}

function showFinalConfiguration() {
    $result = performInstallation();
    
    if ($result['success']) {
        echo '<script>window.location.href = "?step=6";</script>';
        return;
    }
    ?>
    <h3><i class="fas fa-cogs me-2"></i>Final Configuration</h3>
    
    <?php if (!$result['success']): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Installation failed: <?= $result['message'] ?>
        </div>
        <div class="text-end">
            <a href="?step=4" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Go Back
            </a>
        </div>
    <?php endif; ?>
    <?php
}

function showInstallationComplete() {
    ?>
    <div class="text-center">
        <div class="mb-4">
            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
        </div>
        <h3 class="text-success">Installation Complete!</h3>
        <p class="text-muted mb-4">
            AstroGenix has been successfully installed and configured. 
            Your USDT staking platform is ready to use!
        </p>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-globe fa-2x text-primary mb-3"></i>
                        <h5>Visit Your Site</h5>
                        <p class="text-muted">Check out your new platform</p>
                        <a href="/" class="btn btn-primary" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>Open Site
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-user-shield fa-2x text-success mb-3"></i>
                        <h5>Admin Panel</h5>
                        <p class="text-muted">Manage your platform</p>
                        <a href="/admin" class="btn btn-success" target="_blank">
                            <i class="fas fa-tachometer-alt me-2"></i>Admin Panel
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-warning mt-4">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Important:</strong> For security reasons, please delete the <code>install.php</code> file from your server.
        </div>

        <div class="mt-4">
            <h6>Next Steps:</h6>
            <ul class="text-start">
                <li>Configure your USDT wallet address in admin settings</li>
                <li>Customize investment packages</li>
                <li>Set up email templates</li>
                <li>Configure cron jobs for automated tasks</li>
                <li>Test all functionality</li>
            </ul>
        </div>
    </div>
    <?php
}

// Helper Functions

function checkSystemRequirements() {
    $checks = [
        [
            'name' => 'PHP Version',
            'description' => 'PHP 7.4 or higher required',
            'status' => version_compare(PHP_VERSION, '7.4.0', '>=') ? 'ok' : 'error',
            'message' => 'PHP ' . PHP_VERSION
        ],
        [
            'name' => 'PDO Extension',
            'description' => 'Required for database connectivity',
            'status' => extension_loaded('pdo') ? 'ok' : 'error',
            'message' => extension_loaded('pdo') ? 'Available' : 'Not Available'
        ],
        [
            'name' => 'PDO MySQL',
            'description' => 'Required for MySQL database',
            'status' => extension_loaded('pdo_mysql') ? 'ok' : 'error',
            'message' => extension_loaded('pdo_mysql') ? 'Available' : 'Not Available'
        ],
        [
            'name' => 'OpenSSL Extension',
            'description' => 'Required for encryption',
            'status' => extension_loaded('openssl') ? 'ok' : 'error',
            'message' => extension_loaded('openssl') ? 'Available' : 'Not Available'
        ],
        [
            'name' => 'cURL Extension',
            'description' => 'Required for external API calls',
            'status' => extension_loaded('curl') ? 'ok' : 'warning',
            'message' => extension_loaded('curl') ? 'Available' : 'Not Available'
        ],
        [
            'name' => 'GD Extension',
            'description' => 'Required for image processing',
            'status' => extension_loaded('gd') ? 'ok' : 'warning',
            'message' => extension_loaded('gd') ? 'Available' : 'Not Available'
        ],
        [
            'name' => 'Mbstring Extension',
            'description' => 'Required for string handling',
            'status' => extension_loaded('mbstring') ? 'ok' : 'error',
            'message' => extension_loaded('mbstring') ? 'Available' : 'Not Available'
        ],
        [
            'name' => 'File Permissions',
            'description' => 'Write permissions for storage directories',
            'status' => is_writable('.') ? 'ok' : 'error',
            'message' => is_writable('.') ? 'Writable' : 'Not Writable'
        ]
    ];

    $canProceed = true;
    foreach ($checks as $check) {
        if ($check['status'] === 'error') {
            $canProceed = false;
            break;
        }
    }

    return [
        'checks' => $checks,
        'can_proceed' => $canProceed
    ];
}

function processDatabaseConfig($config) {
    try {
        $dsn = "mysql:host={$config['db_host']};port={$config['db_port']}";
        $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Create database if it doesn't exist
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // Test connection to the specific database
        $dsn = "mysql:host={$config['db_host']};port={$config['db_port']};dbname={$config['db_name']}";
        $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
        
        return ['success' => true, 'message' => 'Database connection successful'];
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Database connection failed: ' . $e->getMessage()];
    }
}

function processAdminSetup($config) {
    if ($config['admin_password'] !== $config['admin_password_confirm']) {
        return ['success' => false, 'message' => 'Passwords do not match'];
    }
    
    if (strlen($config['admin_password']) < 8) {
        return ['success' => false, 'message' => 'Password must be at least 8 characters long'];
    }
    
    return ['success' => true, 'message' => 'Admin configuration valid'];
}

function performInstallation() {
    try {
        // Create .env file
        createEnvFile();

        // Create database tables
        createDatabaseTables();

        // Insert initial data
        insertInitialData();

        // Create admin user
        createAdminUser();

        // Set up directories
        setupDirectories();

        // Create installation marker
        file_put_contents('INSTALLED', date('Y-m-d H:i:s'));

        return ['success' => true, 'message' => 'Installation completed successfully'];
    } catch (PDOException $e) {
        $errorMessage = 'Database error: ' . $e->getMessage();

        // Check for specific errors
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            $errorMessage = 'Database schema error: Duplicate column detected. Please reset the database and try again.';
        } elseif (strpos($e->getMessage(), 'already exists') !== false) {
            $errorMessage = 'Database tables already exist. Please reset the database or use the reset script.';
        } elseif (strpos($e->getMessage(), 'Access denied') !== false) {
            $errorMessage = 'Database access denied. Please check your database credentials.';
        } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
            $errorMessage = 'Database does not exist. Please create the database first.';
        }

        return ['success' => false, 'message' => $errorMessage];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Installation error: ' . $e->getMessage()];
    }
}

function createEnvFile() {
    $dbConfig = $_SESSION['db_config'];
    $appConfig = $_SESSION['app_config'];
    
    $envContent = "# AstroGenix Environment Configuration\n";
    $envContent .= "APP_NAME=\"{$appConfig['app_name']}\"\n";
    $envContent .= "APP_ENV=production\n";
    $envContent .= "APP_DEBUG=false\n";
    $envContent .= "APP_URL={$appConfig['app_url']}\n";
    $envContent .= "APP_KEY=" . base64_encode(random_bytes(32)) . "\n\n";
    
    $envContent .= "# Database\n";
    $envContent .= "DB_HOST={$dbConfig['db_host']}\n";
    $envContent .= "DB_PORT={$dbConfig['db_port']}\n";
    $envContent .= "DB_DATABASE={$dbConfig['db_name']}\n";
    $envContent .= "DB_USERNAME={$dbConfig['db_username']}\n";
    $envContent .= "DB_PASSWORD={$dbConfig['db_password']}\n\n";
    
    $envContent .= "# Mail\n";
    $envContent .= "MAIL_HOST={$appConfig['mail_host']}\n";
    $envContent .= "MAIL_PORT={$appConfig['mail_port']}\n";
    $envContent .= "MAIL_USERNAME={$appConfig['mail_username']}\n";
    $envContent .= "MAIL_PASSWORD={$appConfig['mail_password']}\n";
    $envContent .= "MAIL_FROM_ADDRESS={$appConfig['mail_username']}\n";
    $envContent .= "MAIL_FROM_NAME=\"{$appConfig['app_name']}\"\n\n";
    
    $envContent .= "# USDT Wallet\n";
    $envContent .= "USDT_WALLET_ADDRESS={$appConfig['usdt_wallet']}\n";
    
    file_put_contents('.env', $envContent);
}

function createDatabaseTables() {
    $dbConfig = $_SESSION['db_config'];
    $dsn = "mysql:host={$dbConfig['db_host']};port={$dbConfig['db_port']};dbname={$dbConfig['db_name']}";
    $pdo = new PDO($dsn, $dbConfig['db_username'], $dbConfig['db_password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $schema = file_get_contents('database/schema.sql');

    // Remove comments and empty lines, then split by semicolon
    $lines = explode("\n", $schema);
    $cleanedSql = '';

    foreach ($lines as $line) {
        $line = trim($line);
        // Skip comments and empty lines
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }
        $cleanedSql .= $line . "\n";
    }

    // Split SQL into individual statements
    $statements = explode(';', $cleanedSql);

    // Separate table creation from foreign key constraints
    $createStatements = [];
    $alterStatements = [];
    $otherStatements = [];

    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement)) continue;

        if (stripos($statement, 'CREATE TABLE') !== false) {
            $createStatements[] = $statement;
        } elseif (stripos($statement, 'ALTER TABLE') !== false && stripos($statement, 'FOREIGN KEY') !== false) {
            $alterStatements[] = $statement;
        } elseif (stripos($statement, 'CREATE DATABASE') === false && stripos($statement, 'USE ') === false) {
            $otherStatements[] = $statement;
        }
    }

    // Execute in order: other statements, create tables, then foreign keys
    $allStatements = array_merge($otherStatements, $createStatements, $alterStatements);

    foreach ($allStatements as $statement) {
        try {
            $pdo->exec($statement);
        } catch (PDOException $e) {
            // Skip if table already exists or other non-critical errors
            if (strpos($e->getMessage(), 'already exists') === false &&
                strpos($e->getMessage(), 'Duplicate') === false) {
                throw new Exception("SQL Error in statement: " . substr($statement, 0, 100) . "... Error: " . $e->getMessage());
            }
        }
    }
}

function insertInitialData() {
    $dbConfig = $_SESSION['db_config'];
    $dsn = "mysql:host={$dbConfig['db_host']};port={$dbConfig['db_port']};dbname={$dbConfig['db_name']}";
    $pdo = new PDO($dsn, $dbConfig['db_username'], $dbConfig['db_password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $seedData = file_get_contents('database/seed_data.sql');

    // Remove comments and empty lines, then split by semicolon
    $lines = explode("\n", $seedData);
    $cleanedSql = '';

    foreach ($lines as $line) {
        $line = trim($line);
        // Skip comments and empty lines
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }
        $cleanedSql .= $line . "\n";
    }

    // Split SQL into individual statements
    $statements = explode(';', $cleanedSql);

    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                // Skip if data already exists or other non-critical errors
                if (strpos($e->getMessage(), 'Duplicate entry') === false &&
                    strpos($e->getMessage(), 'USE ') === false) {
                    throw new Exception("SQL Error in statement: " . substr($statement, 0, 100) . "... Error: " . $e->getMessage());
                }
            }
        }
    }
}

function createAdminUser() {
    $dbConfig = $_SESSION['db_config'];
    $adminConfig = $_SESSION['admin_config'];
    
    $dsn = "mysql:host={$dbConfig['db_host']};port={$dbConfig['db_port']};dbname={$dbConfig['db_name']}";
    $pdo = new PDO($dsn, $dbConfig['db_username'], $dbConfig['db_password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $passwordHash = password_hash($adminConfig['admin_password'], PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("UPDATE admin_users SET username = ?, email = ?, password_hash = ?, full_name = ? WHERE id = 1");
    $stmt->execute([
        $adminConfig['admin_username'],
        $adminConfig['admin_email'],
        $passwordHash,
        $adminConfig['admin_name']
    ]);
}

function setupDirectories() {
    $directories = [
        'storage/logs',
        'storage/cache',
        'storage/sessions',
        'public/uploads',
        'public/uploads/screenshots',
        'public/uploads/avatars',
        'public/uploads/news'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}
?>
