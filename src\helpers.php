<?php
/**
 * AstroGenix Helper Functions
 */

if (!function_exists('dd')) {
    /**
     * Dump and die
     */
    function dd(...$vars) {
        foreach ($vars as $var) {
            echo '<pre>';
            var_dump($var);
            echo '</pre>';
        }
        die();
    }
}

if (!function_exists('env')) {
    /**
     * Get environment variable
     */
    function env($key, $default = null) {
        return $_ENV[$key] ?? $default;
    }
}

if (!function_exists('config')) {
    /**
     * Get configuration value
     */
    function config($key, $default = null) {
        static $config = [];
        
        $keys = explode('.', $key);
        $file = array_shift($keys);
        
        if (!isset($config[$file])) {
            $configFile = config_path($file . '.php');
            if (file_exists($configFile)) {
                $config[$file] = require $configFile;
            } else {
                return $default;
            }
        }
        
        $value = $config[$file];
        foreach ($keys as $segment) {
            if (is_array($value) && array_key_exists($segment, $value)) {
                $value = $value[$segment];
            } else {
                return $default;
            }
        }
        
        return $value;
    }
}

if (!function_exists('cache')) {
    /**
     * Cache helper
     */
    function cache($key = null, $value = null, $ttl = null) {
        $cache = new \AstroGenix\Core\Cache();
        
        if ($key === null) {
            return $cache;
        }
        
        if ($value === null) {
            return $cache->get($key);
        }
        
        return $cache->set($key, $value, $ttl);
    }
}

if (!function_exists('session')) {
    /**
     * Session helper
     */
    function session($key = null, $value = null) {
        static $session = null;
        
        if ($session === null) {
            $session = new \AstroGenix\Core\Session();
        }
        
        if ($key === null) {
            return $session;
        }
        
        if ($value === null) {
            return $session->get($key);
        }
        
        return $session->set($key, $value);
    }
}

if (!function_exists('request')) {
    /**
     * Request helper
     */
    function request($key = null, $default = null) {
        static $request = null;
        
        if ($request === null) {
            $request = new \AstroGenix\Core\Request();
        }
        
        if ($key === null) {
            return $request;
        }
        
        return $request->input($key, $default);
    }
}

if (!function_exists('response')) {
    /**
     * Response helper
     */
    function response($content = '', $status = 200, $headers = []) {
        $response = new \AstroGenix\Core\Response();
        
        if ($content) {
            $response->setContent($content);
        }
        
        if ($status !== 200) {
            $response->setStatusCode($status);
        }
        
        foreach ($headers as $name => $value) {
            $response->setHeader($name, $value);
        }
        
        return $response;
    }
}

if (!function_exists('redirect')) {
    /**
     * Redirect helper
     */
    function redirect($url, $status = 302) {
        $response = new \AstroGenix\Core\Response();
        $response->redirect($url, $status);
    }
}

if (!function_exists('view')) {
    /**
     * View helper
     */
    function view($template, $data = []) {
        $view = new \AstroGenix\Core\View();
        return $view->render($template, $data);
    }
}

if (!function_exists('logger')) {
    /**
     * Logger helper
     */
    function logger($message = null, $level = 'info', $context = []) {
        static $logger = null;
        
        if ($logger === null) {
            $logger = new \AstroGenix\Core\Logger();
        }
        
        if ($message === null) {
            return $logger;
        }
        
        return $logger->log($level, $message, $context);
    }
}

if (!function_exists('encrypt')) {
    /**
     * Encrypt data
     */
    function encrypt($data) {
        $key = config('app.key');
        $cipher = config('app.cipher', 'AES-256-CBC');
        
        $iv = random_bytes(openssl_cipher_iv_length($cipher));
        $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
}

if (!function_exists('decrypt')) {
    /**
     * Decrypt data
     */
    function decrypt($data) {
        $key = config('app.key');
        $cipher = config('app.cipher', 'AES-256-CBC');
        
        $data = base64_decode($data);
        $ivLength = openssl_cipher_iv_length($cipher);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);
        
        return openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
    }
}

if (!function_exists('hash_password')) {
    /**
     * Hash password
     */
    function hash_password($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
}

if (!function_exists('verify_password')) {
    /**
     * Verify password
     */
    function verify_password($password, $hash) {
        return password_verify($password, $hash);
    }
}

if (!function_exists('generate_token')) {
    /**
     * Generate random token
     */
    function generate_token($length = 32) {
        return bin2hex(random_bytes($length));
    }
}

if (!function_exists('sanitize')) {
    /**
     * Sanitize input
     */
    function sanitize($input) {
        if (is_array($input)) {
            return array_map('sanitize', $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('format_currency')) {
    /**
     * Format currency
     */
    function format_currency($amount, $currency = 'USDT', $decimals = 6) {
        return number_format($amount, $decimals) . ' ' . $currency;
    }
}

if (!function_exists('format_percentage')) {
    /**
     * Format percentage
     */
    function format_percentage($value, $decimals = 2) {
        return number_format($value, $decimals) . '%';
    }
}

if (!function_exists('time_ago')) {
    /**
     * Get time ago string
     */
    function time_ago($datetime) {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) return 'just now';
        if ($time < 3600) return floor($time/60) . ' minutes ago';
        if ($time < 86400) return floor($time/3600) . ' hours ago';
        if ($time < 2592000) return floor($time/86400) . ' days ago';
        if ($time < 31536000) return floor($time/2592000) . ' months ago';
        
        return floor($time/31536000) . ' years ago';
    }
}

if (!function_exists('str_limit')) {
    /**
     * Limit string length
     */
    function str_limit($string, $limit = 100, $end = '...') {
        if (mb_strlen($string) <= $limit) {
            return $string;
        }
        
        return mb_substr($string, 0, $limit) . $end;
    }
}

if (!function_exists('str_slug')) {
    /**
     * Generate URL slug
     */
    function str_slug($string, $separator = '-') {
        $string = strtolower(trim($string));
        $string = preg_replace('/[^a-z0-9-]/', $separator, $string);
        $string = preg_replace('/' . preg_quote($separator) . '+/', $separator, $string);
        
        return trim($string, $separator);
    }
}

if (!function_exists('array_get')) {
    /**
     * Get array value using dot notation
     */
    function array_get($array, $key, $default = null) {
        if (is_null($key)) {
            return $array;
        }
        
        if (isset($array[$key])) {
            return $array[$key];
        }
        
        foreach (explode('.', $key) as $segment) {
            if (!is_array($array) || !array_key_exists($segment, $array)) {
                return $default;
            }
            
            $array = $array[$segment];
        }
        
        return $array;
    }
}

if (!function_exists('array_set')) {
    /**
     * Set array value using dot notation
     */
    function array_set(&$array, $key, $value) {
        if (is_null($key)) {
            return $array = $value;
        }
        
        $keys = explode('.', $key);
        
        while (count($keys) > 1) {
            $key = array_shift($keys);
            
            if (!isset($array[$key]) || !is_array($array[$key])) {
                $array[$key] = [];
            }
            
            $array = &$array[$key];
        }
        
        $array[array_shift($keys)] = $value;
        
        return $array;
    }
}

if (!function_exists('is_json')) {
    /**
     * Check if string is valid JSON
     */
    function is_json($string) {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }
}

if (!function_exists('get_client_ip')) {
    /**
     * Get client IP address
     */
    function get_client_ip() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}

if (!function_exists('generate_uuid')) {
    /**
     * Generate UUID v4
     */
    function generate_uuid() {
        $data = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);
        
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }
}
