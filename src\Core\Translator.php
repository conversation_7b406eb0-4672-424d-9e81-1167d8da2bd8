<?php

namespace AstroGenix\Core;

/**
 * Simple Translator Class
 */
class Translator
{
    private $translations = [];
    private $defaultLanguage = 'en';
    
    public function __construct()
    {
        $this->loadTranslations();
    }
    
    private function loadTranslations(): void
    {
        // English translations
        $this->translations['en'] = [
            'welcome' => 'Welcome to AstroGenix',
            'home' => 'Home',
            'about' => 'About',
            'packages' => 'Investment Packages',
            'news' => 'News',
            'contact' => 'Contact',
            'login' => 'Login',
            'register' => 'Register',
            'dashboard' => 'Dashboard',
            'logout' => 'Logout',
            'invest_now' => 'Invest Now',
            'learn_more' => 'Learn More',
            'get_started' => 'Get Started',
            'daily_profit' => 'Daily Profit',
            'total_return' => 'Total Return',
            'minimum_amount' => 'Minimum Amount',
            'maximum_amount' => 'Maximum Amount',
            'duration' => 'Duration',
            'days' => 'Days',
            'usdt' => 'USDT',
            'balance' => 'Balance',
            'investments' => 'Investments',
            'transactions' => 'Transactions',
            'referrals' => 'Referrals',
            'support' => 'Support',
            'profile' => 'Profile',
            'security' => 'Security',
            'deposit' => 'Deposit',
            'withdraw' => 'Withdraw',
            'email' => 'Email',
            'password' => 'Password',
            'confirm_password' => 'Confirm Password',
            'first_name' => 'First Name',
            'last_name' => 'Last Name',
            'phone' => 'Phone',
            'country' => 'Country',
            'submit' => 'Submit',
            'cancel' => 'Cancel',
            'save' => 'Save',
            'edit' => 'Edit',
            'delete' => 'Delete',
            'view' => 'View',
            'status' => 'Status',
            'amount' => 'Amount',
            'date' => 'Date',
            'type' => 'Type',
            'description' => 'Description',
            'active' => 'Active',
            'inactive' => 'Inactive',
            'pending' => 'Pending',
            'completed' => 'Completed',
            'failed' => 'Failed',
            'cancelled' => 'Cancelled'
        ];
        
        // Russian translations
        $this->translations['ru'] = [
            'welcome' => 'Добро пожаловать в AstroGenix',
            'home' => 'Главная',
            'about' => 'О нас',
            'packages' => 'Инвестиционные пакеты',
            'news' => 'Новости',
            'contact' => 'Контакты',
            'login' => 'Вход',
            'register' => 'Регистрация',
            'dashboard' => 'Панель управления',
            'logout' => 'Выход',
            'invest_now' => 'Инвестировать',
            'learn_more' => 'Узнать больше',
            'get_started' => 'Начать',
            'daily_profit' => 'Ежедневная прибыль',
            'total_return' => 'Общая доходность',
            'minimum_amount' => 'Минимальная сумма',
            'maximum_amount' => 'Максимальная сумма',
            'duration' => 'Длительность',
            'days' => 'Дней',
            'usdt' => 'USDT',
            'balance' => 'Баланс',
            'investments' => 'Инвестиции',
            'transactions' => 'Транзакции',
            'referrals' => 'Рефералы',
            'support' => 'Поддержка',
            'profile' => 'Профиль',
            'security' => 'Безопасность',
            'deposit' => 'Депозит',
            'withdraw' => 'Вывод',
            'email' => 'Email',
            'password' => 'Пароль',
            'confirm_password' => 'Подтвердите пароль',
            'first_name' => 'Имя',
            'last_name' => 'Фамилия',
            'phone' => 'Телефон',
            'country' => 'Страна',
            'submit' => 'Отправить',
            'cancel' => 'Отмена',
            'save' => 'Сохранить',
            'edit' => 'Редактировать',
            'delete' => 'Удалить',
            'view' => 'Просмотр',
            'status' => 'Статус',
            'amount' => 'Сумма',
            'date' => 'Дата',
            'type' => 'Тип',
            'description' => 'Описание',
            'active' => 'Активный',
            'inactive' => 'Неактивный',
            'pending' => 'В ожидании',
            'completed' => 'Завершено',
            'failed' => 'Ошибка',
            'cancelled' => 'Отменено'
        ];
    }
    
    public function translate(string $key, array $params = [], string $language = null): string
    {
        $language = $language ?: $this->defaultLanguage;
        
        if (!isset($this->translations[$language])) {
            $language = $this->defaultLanguage;
        }
        
        $translation = $this->translations[$language][$key] ?? $key;
        
        // Replace parameters
        foreach ($params as $param => $value) {
            $translation = str_replace(':' . $param, $value, $translation);
        }
        
        return $translation;
    }
    
    public function setDefaultLanguage(string $language): void
    {
        $this->defaultLanguage = $language;
    }
    
    public function getSupportedLanguages(): array
    {
        return array_keys($this->translations);
    }
}
