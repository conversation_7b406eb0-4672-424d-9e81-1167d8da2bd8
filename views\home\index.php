<?php
$content = ob_start();
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles"></div>
    </div>
    
    <div class="container">
        <div class="row align-items-center min-vh-100 py-5">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">
                        Take Full Control of Your 
                        <span class="text-gradient">USDT</span> 
                        with AstroGenix
                    </h1>
                    <p class="hero-subtitle">
                        Professional USDT staking platform offering secure and profitable investment 
                        opportunities with daily returns up to 2.5%. Start earning passive income today.
                    </p>
                    
                    <div class="hero-stats mb-4">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="stat-number"><?= number_format($stats['total_users'] ?? 0) ?></h3>
                                    <p class="stat-label">Active Users</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="stat-number">$<?= number_format($stats['total_invested'] ?? 0, 0) ?></h3>
                                    <p class="stat-label">Total Invested</p>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h3 class="stat-number">$<?= number_format($stats['total_earned'] ?? 0, 0) ?></h3>
                                    <p class="stat-label">Profits Paid</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="hero-actions">
                        <?php if (isset($user) && $user): ?>
                            <a href="<?= url('/dashboard') ?>" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                            </a>
                            <a href="<?= url('/dashboard/invest') ?>" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-plus me-2"></i>New Investment
                            </a>
                        <?php else: ?>
                            <a href="<?= url('/register') ?>" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-rocket me-2"></i>Start Investing
                            </a>
                            <a href="<?= url('/packages') ?>" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-chart-line me-2"></i>View Plans
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="hero-visual">
                    <div class="crypto-card">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <div class="crypto-icon">
                                    <i class="fab fa-bitcoin"></i>
                                </div>
                                <div class="ms-3">
                                    <h5 class="mb-0">USDT Staking</h5>
                                    <small class="text-muted">Tether USD</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <h4 class="text-success mb-0">+2.5%</h4>
                                <small class="text-muted">Daily Return</small>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <div class="investment-preview">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="preview-item">
                                            <span class="label">Investment</span>
                                            <span class="value">$1,000</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="preview-item">
                                            <span class="label">Daily Profit</span>
                                            <span class="value text-success">$25.00</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="progress-section mt-3">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Progress</span>
                                        <span>15/30 days</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-gradient" style="width: 50%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="section-title">Why Choose AstroGenix?</h2>
                <p class="section-subtitle">
                    Experience the future of USDT staking with our advanced platform
                </p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4>Secure & Safe</h4>
                    <p>Advanced security measures including SSL encryption, cold storage, and multi-factor authentication to protect your investments.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4>High Returns</h4>
                    <p>Earn up to 2.5% daily returns on your USDT investments with our professionally managed staking strategies.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4>Daily Payouts</h4>
                    <p>Receive your profits daily, automatically credited to your account balance. No waiting, no delays.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4>Referral Program</h4>
                    <p>Earn additional income through our multi-level referral program with commissions up to 5% on direct referrals.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h4>24/7 Support</h4>
                    <p>Round-the-clock customer support to assist you with any questions or issues you may have.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4>Mobile Friendly</h4>
                    <p>Access your investments anytime, anywhere with our fully responsive platform optimized for all devices.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Investment Plans Section -->
<section class="plans-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="section-title">Investment Plans</h2>
                <p class="section-subtitle">
                    Choose the perfect plan that suits your investment goals
                </p>
            </div>
        </div>
        
        <div class="row">
            <?php if (!empty($packages)): ?>
                <?php foreach (array_slice($packages, 0, 3) as $index => $package): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="plan-card <?= $index === 1 ? 'featured' : '' ?>">
                            <?php if ($index === 1): ?>
                                <div class="plan-badge">Most Popular</div>
                            <?php endif; ?>
                            
                            <div class="plan-header">
                                <h3 class="plan-name"><?= htmlspecialchars($package['name']) ?></h3>
                                <div class="plan-return">
                                    <span class="return-rate"><?= number_format($package['daily_percentage'], 1) ?>%</span>
                                    <span class="return-period">Daily</span>
                                </div>
                            </div>
                            
                            <div class="plan-body">
                                <div class="plan-features">
                                    <div class="feature">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>Min: $<?= number_format($package['min_amount'], 0) ?></span>
                                    </div>
                                    <div class="feature">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>Max: $<?= number_format($package['max_amount'], 0) ?></span>
                                    </div>
                                    <div class="feature">
                                        <i class="fas fa-calendar"></i>
                                        <span><?= $package['duration_days'] ?> Days</span>
                                    </div>
                                    <div class="feature">
                                        <i class="fas fa-percentage"></i>
                                        <span><?= number_format($package['total_return_percentage'], 0) ?>% Total Return</span>
                                    </div>
                                </div>
                                
                                <div class="plan-description">
                                    <p><?= htmlspecialchars($package['description']) ?></p>
                                </div>
                            </div>
                            
                            <div class="plan-footer">
                                <?php if (isset($user) && $user): ?>
                                    <a href="<?= url('/dashboard/invest?package=' . $package['id']) ?>" class="btn btn-primary w-100">
                                        Invest Now
                                    </a>
                                <?php else: ?>
                                    <a href="<?= url('/register') ?>" class="btn btn-primary w-100">
                                        Get Started
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?= url('/packages') ?>" class="btn btn-outline-primary btn-lg">
                View All Plans <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</section>

<!-- Live Activity Section -->
<section class="activity-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-6 mb-4">
                <h3 class="mb-4">
                    <i class="fas fa-chart-bar text-primary me-2"></i>
                    Recent Investments
                </h3>
                
                <div class="activity-feed">
                    <?php if (!empty($recent_investments)): ?>
                        <?php foreach (array_slice($recent_investments, 0, 5) as $investment): ?>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-plus-circle text-success"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">
                                        <strong><?= htmlspecialchars($investment['username']) ?></strong>
                                        invested <strong>$<?= number_format($investment['amount'], 2) ?></strong>
                                        in <?= htmlspecialchars($investment['package_name']) ?>
                                    </div>
                                    <div class="activity-time">
                                        <?= date('M j, Y H:i', strtotime($investment['created_at'])) ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted">No recent investments to display.</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <h3 class="mb-4">
                    <i class="fas fa-trophy text-warning me-2"></i>
                    Top Investors
                </h3>
                
                <div class="top-investors">
                    <?php if (!empty($top_investors)): ?>
                        <?php foreach (array_slice($top_investors, 0, 5) as $index => $investor): ?>
                            <div class="investor-item">
                                <div class="investor-rank">
                                    #<?= $index + 1 ?>
                                </div>
                                <div class="investor-info">
                                    <div class="investor-name">
                                        <?= htmlspecialchars(substr($investor['username'], 0, 3) . '***') ?>
                                    </div>
                                    <div class="investor-amount">
                                        $<?= number_format($investor['total_invested'], 0) ?>
                                    </div>
                                </div>
                                <div class="investor-badge">
                                    <?php if ($index === 0): ?>
                                        <i class="fas fa-crown text-warning"></i>
                                    <?php elseif ($index === 1): ?>
                                        <i class="fas fa-medal text-secondary"></i>
                                    <?php elseif ($index === 2): ?>
                                        <i class="fas fa-award text-warning"></i>
                                    <?php else: ?>
                                        <i class="fas fa-star text-muted"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted">No investors to display.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- News Section -->
<?php if (!empty($recent_news)): ?>
<section class="news-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="section-title">Latest News</h2>
                <p class="section-subtitle">
                    Stay updated with the latest platform news and market insights
                </p>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($recent_news as $article): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="news-card">
                        <?php if ($article['featured_image']): ?>
                            <div class="news-image">
                                <img src="<?= asset('uploads/news/' . $article['featured_image']) ?>" alt="<?= htmlspecialchars($article['title']) ?>">
                            </div>
                        <?php endif; ?>
                        
                        <div class="news-content">
                            <div class="news-meta">
                                <span class="news-date">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?= date('M j, Y', strtotime($article['published_at'])) ?>
                                </span>
                                <span class="news-category">
                                    <?= htmlspecialchars($article['category_name']) ?>
                                </span>
                            </div>
                            
                            <h4 class="news-title">
                                <a href="<?= url('/news/' . $article['slug']) ?>">
                                    <?= htmlspecialchars($article['title']) ?>
                                </a>
                            </h4>
                            
                            <p class="news-excerpt">
                                <?= htmlspecialchars(substr($article['excerpt'], 0, 120)) ?>...
                            </p>
                            
                            <a href="<?= url('/news/' . $article['slug']) ?>" class="btn btn-outline-primary btn-sm">
                                Read More <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?= url('/news') ?>" class="btn btn-outline-primary btn-lg">
                View All News <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- CTA Section -->
<section class="cta-section py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2 class="cta-title">Ready to Start Earning?</h2>
                <p class="cta-subtitle">
                    Join thousands of investors who trust AstroGenix with their USDT investments. 
                    Start earning passive income today with our secure and profitable staking platform.
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <?php if (isset($user) && $user): ?>
                    <a href="<?= url('/dashboard/invest') ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>Make Investment
                    </a>
                <?php else: ?>
                    <a href="<?= url('/register') ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-rocket me-2"></i>Get Started Now
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/main.php';
?>
