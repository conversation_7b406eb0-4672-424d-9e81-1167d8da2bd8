<?php

namespace AstroGenix\Controllers;

use AstroGenix\Core\Controller;
use AstroGenix\Models\InvestmentPackage;
use AstroGenix\Models\User;
use AstroGenix\Models\UserInvestment;
use AstroGenix\Models\News;
use AstroGenix\Models\SystemSettings;

/**
 * Home Controller
 */
class HomeController extends Controller
{
    public function index(): void
    {
        $packageModel = new InvestmentPackage();
        $userModel = new User();
        $investmentModel = new UserInvestment();
        $newsModel = new News();
        
        // Get featured investment packages
        $packages = $packageModel->getActivePackages();
        
        // Get platform statistics
        $userStats = $userModel->getUserStatistics();
        $investmentStats = $investmentModel->getSystemInvestmentStats();
        
        // Get recent news
        $recentNews = $newsModel->getRecentNews(3);
        
        // Get top investors (anonymized)
        $topInvestors = $userModel->getTopInvestors(5);
        
        // Get recent investments for activity feed
        $recentInvestments = $investmentModel->getRecentInvestments(10);
        
        $data = [
            'packages' => $packages,
            'stats' => [
                'total_users' => $userStats['total_users'] ?? 0,
                'active_users' => $userStats['active_users'] ?? 0,
                'total_invested' => $userStats['total_invested'] ?? 0,
                'total_earned' => $userStats['total_earned'] ?? 0,
                'total_investments' => $investmentStats['total_investments'] ?? 0,
                'active_investments' => $investmentStats['active_investments'] ?? 0,
            ],
            'recent_news' => $recentNews,
            'top_investors' => $topInvestors,
            'recent_investments' => $recentInvestments,
            'page_title' => 'AstroGenix - Professional USDT Staking Platform',
            'meta_description' => 'Earn passive income with AstroGenix USDT staking. Secure, transparent, and profitable investment packages with daily returns up to 2.5%.'
        ];
        
        $this->view('home/index', $data);
    }
    
    public function about(): void
    {
        $data = [
            'page_title' => 'About AstroGenix - Leading USDT Staking Platform',
            'meta_description' => 'Learn about AstroGenix, the premier USDT staking platform. Our mission, vision, and commitment to providing secure and profitable investment opportunities.'
        ];
        
        $this->view('home/about', $data);
    }
    
    public function packages(): void
    {
        $packageModel = new InvestmentPackage();
        
        // Get all active packages with statistics
        $packages = $packageModel->getAllPackageStatistics();
        
        $data = [
            'packages' => $packages,
            'page_title' => 'Investment Packages - AstroGenix USDT Staking',
            'meta_description' => 'Choose from our range of USDT staking packages. From starter plans to VIP packages, find the perfect investment option for your goals.'
        ];
        
        $this->view('home/packages', $data);
    }
    
    public function packageDetails(): void
    {
        $packageId = $this->request->getParam('id');
        
        if (!$packageId) {
            $this->redirect('/packages');
            return;
        }
        
        $packageModel = new InvestmentPackage();
        $package = $packageModel->find($packageId);
        
        if (!$package || $package['status'] !== 'active') {
            $this->session->setError('Investment package not found');
            $this->redirect('/packages');
            return;
        }
        
        // Get package statistics
        $stats = $packageModel->getPackageStatistics($packageId);
        
        // Calculate example profits for different amounts
        $examples = [];
        $amounts = [$package['min_amount'], $package['min_amount'] * 5, $package['max_amount']];
        
        foreach ($amounts as $amount) {
            if ($amount <= $package['max_amount']) {
                $profitData = $packageModel->calculateProfit($packageId, $amount);
                $examples[] = [
                    'amount' => $amount,
                    'daily_profit' => $profitData['daily_profit'],
                    'total_profit' => $profitData['total_profit'],
                    'duration_days' => $profitData['duration_days']
                ];
            }
        }
        
        $data = [
            'package' => $package,
            'stats' => $stats,
            'examples' => $examples,
            'page_title' => $package['name'] . ' - AstroGenix Investment Package',
            'meta_description' => $package['description']
        ];
        
        $this->view('home/package-details', $data);
    }
    
    public function news(): void
    {
        $newsModel = new News();
        $page = (int) $this->request->get('page', 1);
        $category = $this->request->get('category', '');
        
        // Get news with pagination
        $conditions = ['status' => 'published'];
        if ($category) {
            $conditions['category_slug'] = $category;
        }
        
        $newsData = $newsModel->getPublishedNews($conditions, $page, 12);
        
        // Get news categories
        $categories = $newsModel->getCategories();
        
        $data = [
            'news' => $newsData['data'],
            'pagination' => $newsData['pagination'],
            'categories' => $categories,
            'current_category' => $category,
            'page_title' => 'News & Updates - AstroGenix',
            'meta_description' => 'Stay updated with the latest news, market analysis, and platform updates from AstroGenix.'
        ];
        
        $this->view('home/news', $data);
    }
    
    public function newsArticle(): void
    {
        $slug = $this->request->getParam('slug');
        
        if (!$slug) {
            $this->redirect('/news');
            return;
        }
        
        $newsModel = new News();
        $article = $newsModel->findBySlug($slug);
        
        if (!$article || $article['status'] !== 'published') {
            $this->session->setError('Article not found');
            $this->redirect('/news');
            return;
        }
        
        // Increment view count
        $newsModel->incrementViews($article['id']);
        
        // Get related articles
        $relatedArticles = $newsModel->getRelatedArticles($article['category_id'], $article['id'], 3);
        
        $data = [
            'article' => $article,
            'related_articles' => $relatedArticles,
            'page_title' => $article['title'] . ' - AstroGenix News',
            'meta_description' => $article['excerpt']
        ];
        
        $this->view('home/news-article', $data);
    }
    
    public function faq(): void
    {
        $newsModel = new News();
        
        // Get FAQ articles (stored as news with FAQ category)
        $faqArticles = $newsModel->getFaqArticles();
        
        $data = [
            'faq_articles' => $faqArticles,
            'page_title' => 'Frequently Asked Questions - AstroGenix',
            'meta_description' => 'Find answers to common questions about AstroGenix USDT staking platform, investment packages, and how to get started.'
        ];
        
        $this->view('home/faq', $data);
    }
    
    public function contact(): void
    {
        if ($this->request->isPost()) {
            $this->handleContactForm();
            return;
        }
        
        $data = [
            'page_title' => 'Contact Us - AstroGenix Support',
            'meta_description' => 'Get in touch with AstroGenix support team. We are here to help you with any questions about our USDT staking platform.',
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('home/contact', $data);
    }
    
    private function handleContactForm(): void
    {
        $rules = [
            'name' => 'required|min:2|max:100',
            'email' => 'required|email',
            'subject' => 'required|min:5|max:200',
            'message' => 'required|min:10|max:1000',
            'csrf_token' => 'required'
        ];
        
        $validation = $this->validate($this->request->all(), $rules);
        
        if (!$validation['valid']) {
            $this->session->setError('Please correct the errors and try again');
            $this->redirect('/contact');
            return;
        }
        
        if (!$this->validateCsrfToken($this->request->post('csrf_token'))) {
            $this->session->setError('Invalid security token');
            $this->redirect('/contact');
            return;
        }
        
        // Rate limiting
        $ip = $this->getClientIp();
        if (!$this->rateLimit('contact_form_' . $ip, 3, 3600)) {
            $this->session->setError('Too many contact form submissions. Please try again later.');
            $this->redirect('/contact');
            return;
        }
        
        $data = $this->request->only(['name', 'email', 'subject', 'message']);
        
        // Send email to admin
        $emailSent = $this->sendEmail(
            '<EMAIL>',
            'New Contact Form Submission - ' . $data['subject'],
            'emails/contact-form',
            $data
        );
        
        if ($emailSent) {
            $this->session->setSuccess('Thank you for your message. We will get back to you soon!');
        } else {
            $this->session->setError('Failed to send message. Please try again later.');
        }
        
        $this->redirect('/contact');
    }
    
    public function setLanguage(): void
    {
        $language = $this->request->getParam('lang');
        $supportedLanguages = ['en', 'ru'];

        if (in_array($language, $supportedLanguages)) {
            $this->session->set('language', $language);
        }

        $referer = $this->request->referer() ?: '/';
        $this->redirect($referer);
    }
    
    public function calculator(): void
    {
        if ($this->request->isPost()) {
            $this->handleCalculatorRequest();
            return;
        }
        
        $packageModel = new InvestmentPackage();
        $packages = $packageModel->getActivePackages();
        
        $data = [
            'packages' => $packages,
            'page_title' => 'Investment Calculator - AstroGenix',
            'meta_description' => 'Calculate your potential earnings with AstroGenix investment packages. See daily and total profits for different investment amounts.'
        ];
        
        $this->view('home/calculator', $data);
    }
    
    private function handleCalculatorRequest(): void
    {
        $packageId = (int) $this->request->post('package_id');
        $amount = (float) $this->request->post('amount');
        
        if (!$packageId || !$amount) {
            $this->error('Invalid input parameters');
            return;
        }
        
        $packageModel = new InvestmentPackage();
        
        try {
            $profitData = $packageModel->calculateProfit($packageId, $amount);
            $this->success('Calculation completed', $profitData);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }
}
