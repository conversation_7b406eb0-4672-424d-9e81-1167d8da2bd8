<?php

namespace AstroGenix\Core;

/**
 * HTTP Response Handler
 */
class Response
{
    private $statusCode = 200;
    private $headers = [];
    private $content = '';
    
    public function setStatusCode(int $code): self
    {
        $this->statusCode = $code;
        return $this;
    }
    
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }
    
    public function setHeader(string $name, string $value): self
    {
        $this->headers[$name] = $value;
        return $this;
    }
    
    public function getHeader(string $name): ?string
    {
        return $this->headers[$name] ?? null;
    }
    
    public function getHeaders(): array
    {
        return $this->headers;
    }
    
    public function setContent(string $content): self
    {
        $this->content = $content;
        return $this;
    }
    
    public function getContent(): string
    {
        return $this->content;
    }
    
    public function json(array $data, int $statusCode = 200): void
    {
        $this->setStatusCode($statusCode)
             ->setHeader('Content-Type', 'application/json')
             ->setContent(json_encode($data, JSON_UNESCAPED_UNICODE));
        
        $this->send();
    }
    
    public function html(string $content, int $statusCode = 200): void
    {
        $this->setStatusCode($statusCode)
             ->setHeader('Content-Type', 'text/html; charset=UTF-8')
             ->setContent($content);
        
        $this->send();
    }
    
    public function redirect(string $url, int $statusCode = 302): void
    {
        $this->setStatusCode($statusCode)
             ->setHeader('Location', $url);
        
        $this->send();
        exit;
    }
    
    public function back(): void
    {
        $referer = $_SERVER['HTTP_REFERER'] ?? '/';
        $this->redirect($referer);
    }
    
    public function download(string $filePath, string $filename = null): void
    {
        if (!file_exists($filePath)) {
            $this->setStatusCode(404)->send();
            return;
        }
        
        $filename = $filename ?: basename($filePath);
        $mimeType = $this->getMimeType($filePath);
        
        $this->setHeader('Content-Type', $mimeType)
             ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
             ->setHeader('Content-Length', (string) filesize($filePath))
             ->setHeader('Cache-Control', 'no-cache, must-revalidate')
             ->setHeader('Expires', 'Sat, 26 Jul 1997 05:00:00 GMT');
        
        $this->sendHeaders();
        readfile($filePath);
        exit;
    }
    
    public function stream(string $filePath): void
    {
        if (!file_exists($filePath)) {
            $this->setStatusCode(404)->send();
            return;
        }
        
        $mimeType = $this->getMimeType($filePath);
        $fileSize = filesize($filePath);
        
        $this->setHeader('Content-Type', $mimeType)
             ->setHeader('Content-Length', (string) $fileSize)
             ->setHeader('Accept-Ranges', 'bytes');
        
        // Handle range requests for video/audio streaming
        if (isset($_SERVER['HTTP_RANGE'])) {
            $this->handleRangeRequest($filePath, $fileSize);
        } else {
            $this->sendHeaders();
            readfile($filePath);
        }
        
        exit;
    }
    
    public function cache(int $seconds): self
    {
        $this->setHeader('Cache-Control', 'public, max-age=' . $seconds)
             ->setHeader('Expires', gmdate('D, d M Y H:i:s', time() + $seconds) . ' GMT');
        
        return $this;
    }
    
    public function noCache(): self
    {
        $this->setHeader('Cache-Control', 'no-cache, no-store, must-revalidate')
             ->setHeader('Pragma', 'no-cache')
             ->setHeader('Expires', '0');
        
        return $this;
    }
    
    public function cors(array $options = []): self
    {
        $defaults = [
            'origin' => '*',
            'methods' => 'GET, POST, PUT, DELETE, OPTIONS',
            'headers' => 'Content-Type, Authorization, X-Requested-With',
            'credentials' => 'false'
        ];
        
        $options = array_merge($defaults, $options);
        
        $this->setHeader('Access-Control-Allow-Origin', $options['origin'])
             ->setHeader('Access-Control-Allow-Methods', $options['methods'])
             ->setHeader('Access-Control-Allow-Headers', $options['headers'])
             ->setHeader('Access-Control-Allow-Credentials', $options['credentials']);
        
        return $this;
    }
    
    public function send(): void
    {
        $this->sendHeaders();
        echo $this->content;
        
        if (function_exists('fastcgi_finish_request')) {
            fastcgi_finish_request();
        }
    }
    
    private function sendHeaders(): void
    {
        if (headers_sent()) {
            return;
        }
        
        http_response_code($this->statusCode);
        
        foreach ($this->headers as $name => $value) {
            header($name . ': ' . $value);
        }
    }
    
    private function getMimeType(string $filePath): string
    {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        $mimeTypes = [
            'txt' => 'text/plain',
            'htm' => 'text/html',
            'html' => 'text/html',
            'php' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'xml' => 'application/xml',
            'swf' => 'application/x-shockwave-flash',
            'flv' => 'video/x-flv',
            'png' => 'image/png',
            'jpe' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'jpg' => 'image/jpeg',
            'gif' => 'image/gif',
            'bmp' => 'image/bmp',
            'ico' => 'image/vnd.microsoft.icon',
            'tiff' => 'image/tiff',
            'tif' => 'image/tiff',
            'svg' => 'image/svg+xml',
            'svgz' => 'image/svg+xml',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            'exe' => 'application/x-msdownload',
            'msi' => 'application/x-msdownload',
            'cab' => 'application/vnd.ms-cab-compressed',
            'mp3' => 'audio/mpeg',
            'qt' => 'video/quicktime',
            'mov' => 'video/quicktime',
            'pdf' => 'application/pdf',
            'psd' => 'image/vnd.adobe.photoshop',
            'ai' => 'application/postscript',
            'eps' => 'application/postscript',
            'ps' => 'application/postscript',
            'doc' => 'application/msword',
            'rtf' => 'application/rtf',
            'xls' => 'application/vnd.ms-excel',
            'ppt' => 'application/vnd.ms-powerpoint',
            'odt' => 'application/vnd.oasis.opendocument.text',
            'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
        ];
        
        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
    
    private function handleRangeRequest(string $filePath, int $fileSize): void
    {
        $range = $_SERVER['HTTP_RANGE'];
        $ranges = explode('=', $range, 2);
        
        if (count($ranges) != 2 || $ranges[0] != 'bytes') {
            $this->setStatusCode(416)->send();
            return;
        }
        
        $range = explode('-', $ranges[1], 2);
        $start = $range[0] === '' ? 0 : intval($range[0]);
        $end = $range[1] === '' ? $fileSize - 1 : intval($range[1]);
        
        if ($start > $end || $start >= $fileSize || $end >= $fileSize) {
            $this->setStatusCode(416)->send();
            return;
        }
        
        $this->setStatusCode(206)
             ->setHeader('Content-Range', "bytes $start-$end/$fileSize")
             ->setHeader('Content-Length', (string) ($end - $start + 1));
        
        $this->sendHeaders();
        
        $file = fopen($filePath, 'rb');
        fseek($file, $start);
        
        $remaining = $end - $start + 1;
        while ($remaining > 0 && !feof($file)) {
            $chunk = min(8192, $remaining);
            echo fread($file, $chunk);
            $remaining -= $chunk;
            flush();
        }
        
        fclose($file);
    }
}
